/*!
@page middleware_log Middleware Change Log

@section sd SD Card driver for MCUXpresso SDK
  The current driver version is 2.4.0.

  - 2.4.0
    - Improvements
      - Removed deprecated api in sd driver.
      - Added new api SD_PollingCardStatusBusy for application polling card status.
      - Improved the read/write/erase function flow.
      - Improved the signal line voltage switch flow.
      - Added powerOnDelayMS/powerOffDelayMS in sd_usr_param_t to allow redefine the default power on/off delay.
      - Added mutual exclusive access for init/deinit/read/write/erase function.
      - Fixed the driver strength configurations missed when timing mode switch to non SDR50/SDR104 mode.
      - Fixed violations of MISRA C-2012 rule 4.7, 17.7, 10.7, 10.4, 13.5, 14.4.

  - 2.3.3
    - Improvements
      - Added host SDR timing mode capability validation during card initialization.
      - Added plling card ready for data status when transfer data failed.
      - Used cache line size align buffer for SD initialization api.
    - Bug Fixes
      - Fixed violations of MISRA C-2012 rule 11.9, 15.7, 4.7, 16.4, 10.1, 10.3, 10.4, 11.3, 14.4, 10.6, 17.7, 16.1, 16.3.

  - 2.3.2
    - Improvements
      - Moved power off function after card detect in SD_Init for DAT3 detect card feature.

  - 2.3.1
    - Improvements
      - Removed the dead loop while polling DAT0 and CMD13 instead of using timeout mechanism.

  - 2.3.0
    - Improvements
      - Marked api SD_HostReset/SD_PowerOnCard/SD_PowerOffCard/SD_WaitCardDetectStatus as deprecated.
      - Added new api SD_SetCardPower/SD_PollingCardDetectStatus/SD_HostDoReset.
      - Added internalBuffer in sd_card_t and removed rawCid/rawCsd/rawScr.
      - Added retuning support during data transfer under SDR50/SDR104 mode.
      - Increased the read/write blocks failed retry times for stability.
      - Added delay while retry the ACMD41 for stability.

  - 2.2.12
    - Improvements
      - Increased the sd io driver strength for SD2.0 card.
    - Bug Fixes
      - Fixed the build warning by changing the old style function declaration static
        status_t inline to static inline status_t(found by adding
        -Wold-style-declaration in armgcc build flag).

  - 2.2.10
    - Bug Fixes
      - Added event value check for all the FreeRTOS events to fix program hangs
        when a card event occurs before create.

  - 2.2.7
    - Bug Fixes
      - Fixed MDK 66-D warning.

  - 2.2.5
    - Improvements
      - Added SD_ReadStatus api to get 512bit SD status.
      - Added error log support in sdcard functions.
      - Added SDMMC_ENABLE_SOFTWARE_TUNING to enable/disable software tuning and it is disabled by default.

  - 2.2.4
    - Bug Fixes
      - Fixed DDR mode data sequence miss issue, which is caused by NIBBLE_POS.
    - Improvements
      - Increased g_sdmmc 512byte to improve the performance when application use a non-word align data buffer address.
      - Enabled auto cmd12 for SD read/write.

  - 2.2.3
    - Bug Fixes
      - Added response check for send operation condition command. If not checked, the card may occasionally init fail.

  - 2.2.1
    - Improvements
      - Kept SD_Init function for forward compatibility.

  - 2.2.0
    - Improvements
      - Separated the SD/MMC/SDIO init API to xxx_CardInit/xxx_HostInit.
      - SD_Init/SDIO_Init will be deprecated in the next version.

  - 2.1.6
    - Improvements
      - Enhanced SD IO default driver strength.

  - 2.1.5
  	- Bug Fixes
      - Fixed Coverity issue.
      - Fixed SD v1.x card write fail issue. It was caused by the block length set error.
      - Fixed card cannot detect dynamically.

  - 2.1.3
    - Bug Fixes
      - Fixed Non high-speed sdcard init fail at switch to high speed.
    - Improvements
      - Added Delay for SDCard power up.

  - 2.1.2
    - Improvements
      - Improved SDMMC to support SD v3.0.

  - 2.1.1
    - Bug Fixes
      - Fixed the bit mask error in the SD card switch to high speed function.
    - Improvements
      - Optimized the SD card initialization function.

  - 2.1.0
    - Bug Fixes
      - Changed the callback mechanism when sending a command.
      - Fixed the performance low issue when transferring data.
    - Improvements
      - Changed the name of some error codes returned by internal function.
      - Merged all host related attributes to one structure.

  - 2.0.0
    - Initial version.

*/
