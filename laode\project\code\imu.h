#ifndef __IMU_H
#define __IMU_H

#include "zf_common_headfile.h"

typedef struct {
   float gyro_x;
   float gyro_y;
   float gyro_z;
   float acc_x;
   float acc_y;
   float acc_z;
   float mag_x;
   float mag_y;
   float mag_z;
} arhs_source_param_t;


typedef struct {
   float Xdata;
   float Ydata;
   float Zdata;
} gyro_zero_param_t;
void gyro_reset_angle(void);
void jiaodu();
extern arhs_source_param_t source_data;
extern float z_gyro_last,z_gyro_now,angle_z;
extern float angle;
extern float imu_Dat_old;
extern float imu_Dat;
extern float x_v;
extern float y_v;
extern uint8 imu_init_flag;
void get_velocity();
void imura_zeroBias(void);
void get_pose();
void gyro_coord();
void imu_init(void);
float get_imu(float dat);

#endif