./objects/zf_driver_sdio.o: ..\..\libraries\zf_driver\zf_driver_sdio.c \
  ..\..\libraries\sdk\utilities\debug_console\fsl_debug_console.h \
  ..\..\libraries\sdk\drives\fsl_common.h \
  ..\..\libraries\sdk\deceive\fsl_device_registers.h \
  ..\..\libraries\sdk\deceive\MIMXRT1064.h \
  ..\..\libraries\sdk\CMSIS\Include\core_cm7.h \
  ..\..\libraries\sdk\deceive\system_MIMXRT1064.h \
  ..\..\libraries\sdk\deceive\MIMXRT1064_features.h \
  ..\..\libraries\sdk\drives\fsl_common_arm.h \
  ..\..\libraries\sdk\drives\fsl_clock.h \
  ..\..\libraries\components\fatfs\source\fsl_sd_disk\fsl_sd_disk.h \
  ..\..\libraries\components\fatfs\source\ff.h \
  ..\..\libraries\components\fatfs\source\ffconf.h \
  ..\..\libraries\components\fatfs\source\diskio.h \
  ..\..\libraries\components\sdmmc\sd\fsl_sd.h \
  ..\..\libraries\components\sdmmc\common\fsl_sdmmc_common.h \
  ..\..\libraries\components\sdmmc\host\usdhc\fsl_sdmmc_host.h \
  ..\..\libraries\components\sdmmc\osa\fsl_sdmmc_osa.h \
  ..\..\libraries\sdk\components\osa\fsl_os_abstraction.h \
  ..\..\libraries\sdk\components\osa\fsl_os_abstraction_config.h \
  ..\..\libraries\sdk\components\lists\fsl_component_generic_list.h \
  ..\..\libraries\sdk\components\osa\fsl_os_abstraction_bm.h \
  ..\..\libraries\sdk\drives\fsl_usdhc.h \
  ..\..\libraries\components\sdmmc\common\fsl_sdmmc_spec.h \
  ..\..\libraries\sdk\drives\fsl_iomuxc.h \
  ..\..\libraries\components\sdmmc\sdmmc_config.h \
  ..\..\libraries\sdk\board\clock_config.h \
  ..\..\libraries\zf_common\zf_common_clock.h \
  ..\..\libraries\zf_common\zf_common_typedef.h \
  ..\..\libraries\zf_common\zf_common_debug.h \
  ..\..\libraries\zf_driver\zf_driver_gpio.h \
  ..\..\libraries\sdk\drives\fsl_gpio.h \
  ..\..\libraries\zf_driver\zf_driver_delay.h \
  ..\..\libraries\zf_driver\zf_driver_sdio.h
