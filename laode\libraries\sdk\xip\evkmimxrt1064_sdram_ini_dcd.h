/*
 * Copyright 2020 NXP
 * All rights reserved.
 *
 * SPDX-License-Identifier: BSD-3-Clause
 */

/***********************************************************************************************************************
 * This file was generated by the MCUXpresso Config Tools. Any manual edits made to this file
 * will be overwritten if the respective MCUXpresso Config Tools is used to update this file.
 **********************************************************************************************************************/

#ifndef __EVKMIMXRT1064_SDRAM_INI_DCD__
#define __EVKMIMXRT1064_SDRAM_INI_DCD__

#include <stdint.h>

/*! @name Driver version */
/*@{*/
/*! @brief XIP_BOARD driver version 2.0.1. */
#define FSL_XIP_BOARD_DRIVER_VERSION (MAKE_VERSION(2, 0, 1))
/*@}*/

/*************************************
 *  DCD Data
 *************************************/
#define DCD_TAG_HEADER (0xD2)
#define DCD_VERSION (0x41)
#define DCD_TAG_HEADER_SHIFT (24)
#define DCD_ARRAY_SIZE 1

#endif /* __DCD__ */
