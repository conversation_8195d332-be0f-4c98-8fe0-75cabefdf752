--cpu Cortex-M7.fp.dp
".\objects\main.o"
".\objects\isr.o"
".\objects\serial.o"
".\objects\system_init.o"
".\objects\swj.o"
".\objects\vofa.o"
".\objects\zf_common_clock.o"
".\objects\zf_common_debug.o"
".\objects\zf_common_fifo.o"
".\objects\zf_common_font.o"
".\objects\zf_common_function.o"
".\objects\zf_common_interrupt.o"
".\objects\zf_common_vector.o"
".\objects\zf_driver_adc.o"
".\objects\zf_driver_csi.o"
".\objects\zf_driver_delay.o"
".\objects\zf_driver_encoder.o"
".\objects\zf_driver_exti.o"
".\objects\zf_driver_flash.o"
".\objects\zf_driver_flexio_csi.o"
".\objects\zf_driver_gpio.o"
".\objects\zf_driver_iic.o"
".\objects\zf_driver_pit.o"
".\objects\zf_driver_uart.o"
".\objects\zf_driver_pwm.o"
".\objects\zf_driver_romapi.o"
".\objects\zf_driver_sdio.o"
".\objects\zf_driver_soft_iic.o"
".\objects\zf_driver_soft_spi.o"
".\objects\zf_driver_spi.o"
".\objects\zf_driver_timer.o"
".\objects\zf_driver_usb_cdc.o"
".\objects\zf_device_absolute_encoder.o"
".\objects\zf_device_bluetooth_ch9141.o"
".\objects\zf_device_camera.o"
".\objects\zf_device_gps_tau1201.o"
".\objects\zf_device_icm20602.o"
".\objects\zf_device_imu963ra.o"
".\objects\zf_device_imu660ra.o"
".\objects\zf_device_ips114.o"
".\objects\zf_device_mt9v03x.o"
".\objects\zf_device_ips200.o"
".\objects\zf_device_key.o"
".\objects\zf_device_mpu6050.o"
".\objects\zf_device_mt9v03x_flexio.o"
".\objects\zf_device_oled.o"
".\objects\zf_device_ov7725.o"
".\objects\zf_device_scc8660.o"
".\objects\zf_device_scc8660_flexio.o"
".\objects\zf_device_tft180.o"
".\objects\zf_device_tsl1401.o"
".\objects\zf_device_type.o"
".\objects\zf_device_virtual_oscilloscope.o"
".\objects\zf_device_wifi_uart.o"
".\objects\zf_device_wireless_uart.o"
"..\..\libraries\zf_device\zf_device_config.lib"
".\objects\zf_device_dl1a.o"
".\objects\zf_device_dl1b.o"
".\objects\zf_device_wifi_spi.o"
".\objects\seekfree_assistant.o"
".\objects\seekfree_assistant_interface.o"
".\objects\fsl_adc.o"
".\objects\fsl_adc_etc.o"
".\objects\fsl_aipstz.o"
".\objects\fsl_aoi.o"
".\objects\fsl_bee.o"
".\objects\fsl_cache.o"
".\objects\fsl_clock.o"
".\objects\fsl_cmp.o"
".\objects\fsl_common.o"
".\objects\fsl_common_arm.o"
".\objects\fsl_csi.o"
".\objects\fsl_dcdc.o"
".\objects\fsl_dcp.o"
".\objects\fsl_dmamux.o"
".\objects\fsl_edma.o"
".\objects\fsl_elcdif.o"
".\objects\fsl_enc.o"
".\objects\fsl_enet.o"
".\objects\fsl_ewm.o"
".\objects\fsl_flexcan.o"
".\objects\fsl_flexcan_edma.o"
".\objects\fsl_flexio.o"
".\objects\fsl_flexio_camera.o"
".\objects\fsl_flexio_camera_edma.o"
".\objects\fsl_flexio_i2c_master.o"
".\objects\fsl_flexio_i2s.o"
".\objects\fsl_flexio_i2s_edma.o"
".\objects\fsl_flexio_mculcd.o"
".\objects\fsl_flexio_mculcd_edma.o"
".\objects\fsl_flexio_spi.o"
".\objects\fsl_flexio_spi_edma.o"
".\objects\fsl_flexio_uart.o"
".\objects\fsl_flexio_uart_edma.o"
".\objects\fsl_flexram.o"
".\objects\fsl_flexram_allocate.o"
".\objects\fsl_flexspi.o"
".\objects\fsl_gpc.o"
".\objects\fsl_gpio.o"
".\objects\fsl_gpt.o"
".\objects\fsl_kpp.o"
".\objects\fsl_lpi2c.o"
".\objects\fsl_lpi2c_edma.o"
".\objects\fsl_lpspi.o"
".\objects\fsl_lpspi_edma.o"
".\objects\fsl_lpuart.o"
".\objects\fsl_lpuart_edma.o"
".\objects\fsl_ocotp.o"
".\objects\fsl_pit.o"
".\objects\fsl_pmu.o"
".\objects\fsl_pwm.o"
".\objects\fsl_pxp.o"
".\objects\fsl_qtmr.o"
".\objects\fsl_rtwdog.o"
".\objects\fsl_sai.o"
".\objects\fsl_sai_edma.o"
".\objects\fsl_semc.o"
".\objects\fsl_snvs_hp.o"
".\objects\fsl_snvs_lp.o"
".\objects\fsl_spdif.o"
".\objects\fsl_spdif_edma.o"
".\objects\fsl_src.o"
".\objects\fsl_tempmon.o"
".\objects\fsl_trng.o"
".\objects\fsl_tsc.o"
".\objects\fsl_usdhc.o"
".\objects\fsl_wdog.o"
".\objects\fsl_xbara.o"
".\objects\fsl_xbarb.o"
".\objects\board.o"
".\objects\clock_config.o"
".\objects\fsl_flexspi_nor_boot.o"
".\objects\evkmimxrt1064_sdram_ini_dcd.o"
".\objects\evkmimxrt1064_flexspi_nor_config.o"
".\objects\system_mimxrt1064.o"
".\objects\startup_mimxrt1064.o"
".\objects\fsl_assert.o"
".\objects\fsl_os_abstraction_bm.o"
".\objects\fsl_component_generic_list.o"
".\objects\fsl_adapter_lpuart.o"
".\objects\fsl_debug_console.o"
".\objects\diskio.o"
".\objects\ff.o"
".\objects\ffsystem.o"
".\objects\ffunicode.o"
".\objects\fsl_sd_disk.o"
".\objects\sdmmc_config.o"
".\objects\fsl_sdmmc_common.o"
".\objects\fsl_sdmmc_host.o"
".\objects\fsl_sdmmc_osa.o"
".\objects\fsl_sd.o"
".\objects\usb_device_ehci.o"
".\objects\usb_phy.o"
".\objects\usb_device_dci.o"
".\objects\usb_device_cdc_acm.o"
".\objects\usb_device_ch9.o"
".\objects\usb_device_class.o"
".\objects\usb_device_descriptor.o"
--library_type=microlib --diag_suppress 6314,6329 --strict --scatter ".\scf\MIMXRT1064xxxxx_flexspi_nor.scf"
--remove --entry=Reset_Handler --summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\Listings\rt1064.map" -o .\Objects\rt1064.axf