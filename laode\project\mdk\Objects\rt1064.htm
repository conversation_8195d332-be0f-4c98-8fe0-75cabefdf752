<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\rt1064.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\rt1064.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 6220000: Last Updated: Sat Aug  2 00:23:23 2025
<BR><P>
<H3>Maximum Stack Usage =       1480 bytes + Unknown(Functions without stacksize, Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; display_value_float &rArr; ips200_show_float &rArr; ips200_show_string &rArr; ips200_show_char &rArr; ips200_set_region &rArr; ips200_write_command &rArr; spi_write_8bit &rArr; spi_write_8bit_array &rArr; spi_write &rArr; LPSPI_MasterTransferBlocking &rArr; LPSPI_CheckTransferArgument &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[0]">Reset_Handler</a>
 <LI><a href="#[85]">ACMP1_IRQHandler</a>
 <LI><a href="#[86]">ACMP2_IRQHandler</a>
 <LI><a href="#[87]">ACMP3_IRQHandler</a>
 <LI><a href="#[88]">ACMP4_IRQHandler</a>
 <LI><a href="#[4d]">ADC1_IRQHandler</a>
 <LI><a href="#[4e]">ADC2_IRQHandler</a>
 <LI><a href="#[83]">ADC_ETC_ERROR_IRQ_IRQHandler</a>
 <LI><a href="#[80]">ADC_ETC_IRQ0_IRQHandler</a>
 <LI><a href="#[81]">ADC_ETC_IRQ1_IRQHandler</a>
 <LI><a href="#[82]">ADC_ETC_IRQ2_IRQHandler</a>
 <LI><a href="#[41]">BEE_IRQHandler</a>
 <LI><a href="#[69]">CCM_1_IRQHandler</a>
 <LI><a href="#[6a]">CCM_2_IRQHandler</a>
 <LI><a href="#[1d]">CORE_IRQHandler</a>
 <LI><a href="#[3b]">CSU_IRQHandler</a>
 <LI><a href="#[1b]">CTI0_ERROR_IRQHandler</a>
 <LI><a href="#[1c]">CTI1_ERROR_IRQHandler</a>
 <LI><a href="#[4f]">DCDC_IRQHandler</a>
 <LI><a href="#[3c]">DCP_IRQHandler</a>
 <LI><a href="#[3d]">DCP_VMI_IRQHandler</a>
 <LI><a href="#[bc]">DMA_ERROR_DriverIRQHandler</a>
 <LI><a href="#[a8]">DefaultISR</a>
 <LI><a href="#[8b]">ENC1_IRQHandler</a>
 <LI><a href="#[8c]">ENC2_IRQHandler</a>
 <LI><a href="#[8d]">ENC3_IRQHandler</a>
 <LI><a href="#[8e]">ENC4_IRQHandler</a>
 <LI><a href="#[68]">EWM_IRQHandler</a>
 <LI><a href="#[30]">FLEXRAM_IRQHandler</a>
 <LI><a href="#[d6]">FLEXSPI2_DriverIRQHandler</a>
 <LI><a href="#[d7]">FLEXSPI_DriverIRQHandler</a>
 <LI><a href="#[6b]">GPC_IRQHandler</a>
 <LI><a href="#[51]">GPIO10_IRQHandler</a>
 <LI><a href="#[52]">GPIO1_INT0_IRQHandler</a>
 <LI><a href="#[53]">GPIO1_INT1_IRQHandler</a>
 <LI><a href="#[54]">GPIO1_INT2_IRQHandler</a>
 <LI><a href="#[55]">GPIO1_INT3_IRQHandler</a>
 <LI><a href="#[56]">GPIO1_INT4_IRQHandler</a>
 <LI><a href="#[57]">GPIO1_INT5_IRQHandler</a>
 <LI><a href="#[58]">GPIO1_INT6_IRQHandler</a>
 <LI><a href="#[59]">GPIO1_INT7_IRQHandler</a>
 <LI><a href="#[5f]">GPIO3_Combined_16_31_IRQHandler</a>
 <LI><a href="#[60]">GPIO4_Combined_0_15_IRQHandler</a>
 <LI><a href="#[61]">GPIO4_Combined_16_31_IRQHandler</a>
 <LI><a href="#[62]">GPIO5_Combined_0_15_IRQHandler</a>
 <LI><a href="#[63]">GPIO5_Combined_16_31_IRQHandler</a>
 <LI><a href="#[a7]">GPIO6_7_8_9_IRQHandler</a>
 <LI><a href="#[33]">GPR_IRQ_IRQHandler</a>
 <LI><a href="#[6e]">GPT1_IRQHandler</a>
 <LI><a href="#[6f]">GPT2_IRQHandler</a>
 <LI><a href="#[31]">KPP_IRQHandler</a>
 <LI><a href="#[34]">LCDIF_IRQHandler</a>
 <LI><a href="#[47]">PMU_EVENT_IRQHandler</a>
 <LI><a href="#[70]">PWM1_0_IRQHandler</a>
 <LI><a href="#[71]">PWM1_1_IRQHandler</a>
 <LI><a href="#[72]">PWM1_2_IRQHandler</a>
 <LI><a href="#[73]">PWM1_3_IRQHandler</a>
 <LI><a href="#[74]">PWM1_FAULT_IRQHandler</a>
 <LI><a href="#[93]">PWM2_0_IRQHandler</a>
 <LI><a href="#[94]">PWM2_1_IRQHandler</a>
 <LI><a href="#[95]">PWM2_2_IRQHandler</a>
 <LI><a href="#[96]">PWM2_3_IRQHandler</a>
 <LI><a href="#[97]">PWM2_FAULT_IRQHandler</a>
 <LI><a href="#[98]">PWM3_0_IRQHandler</a>
 <LI><a href="#[99]">PWM3_1_IRQHandler</a>
 <LI><a href="#[9a]">PWM3_2_IRQHandler</a>
 <LI><a href="#[9b]">PWM3_3_IRQHandler</a>
 <LI><a href="#[9c]">PWM3_FAULT_IRQHandler</a>
 <LI><a href="#[9d]">PWM4_0_IRQHandler</a>
 <LI><a href="#[9e]">PWM4_1_IRQHandler</a>
 <LI><a href="#[9f]">PWM4_2_IRQHandler</a>
 <LI><a href="#[a0]">PWM4_3_IRQHandler</a>
 <LI><a href="#[a1]">PWM4_FAULT_IRQHandler</a>
 <LI><a href="#[36]">PXP_IRQHandler</a>
 <LI><a href="#[67]">RTWDOG_IRQHandler</a>
 <LI><a href="#[6d]">Reserved115_IRQHandler</a>
 <LI><a href="#[89]">Reserved143_IRQHandler</a>
 <LI><a href="#[8a]">Reserved144_IRQHandler</a>
 <LI><a href="#[a5]">Reserved171_IRQHandler</a>
 <LI><a href="#[3e]">Reserved68_IRQHandler</a>
 <LI><a href="#[48]">Reserved78_IRQHandler</a>
 <LI><a href="#[50]">Reserved86_IRQHandler</a>
 <LI><a href="#[77]">SEMC_IRQHandler</a>
 <LI><a href="#[40]">SJC_IRQHandler</a>
 <LI><a href="#[38]">SNVS_HP_WRAPPER_IRQHandler</a>
 <LI><a href="#[39]">SNVS_HP_WRAPPER_TZ_IRQHandler</a>
 <LI><a href="#[3a]">SNVS_LP_WRAPPER_IRQHandler</a>
 <LI><a href="#[6c]">SRC_IRQHandler</a>
 <LI><a href="#[49]">TEMP_LOW_HIGH_IRQHandler</a>
 <LI><a href="#[4a]">TEMP_PANIC_IRQHandler</a>
 <LI><a href="#[8f]">TMR1_IRQHandler</a>
 <LI><a href="#[90]">TMR2_IRQHandler</a>
 <LI><a href="#[91]">TMR3_IRQHandler</a>
 <LI><a href="#[92]">TMR4_IRQHandler</a>
 <LI><a href="#[3f]">TRNG_IRQHandler</a>
 <LI><a href="#[32]">TSC_DIG_IRQHandler</a>
 <LI><a href="#[4b]">USB_PHY1_IRQHandler</a>
 <LI><a href="#[4c]">USB_PHY2_IRQHandler</a>
 <LI><a href="#[66]">WDOG1_IRQHandler</a>
 <LI><a href="#[37]">WDOG2_IRQHandler</a>
 <LI><a href="#[7e]">XBAR1_IRQ_0_1_IRQHandler</a>
 <LI><a href="#[7f]">XBAR1_IRQ_2_3_IRQHandler</a>
 <LI><a href="#[9]">SysTick_Handler</a>
 <LI><a href="#[a]">DMA0_DMA16_IRQHandler</a>
 <LI><a href="#[b]">DMA1_DMA17_IRQHandler</a>
 <LI><a href="#[c]">DMA2_DMA18_IRQHandler</a>
 <LI><a href="#[d]">DMA3_DMA19_IRQHandler</a>
 <LI><a href="#[e]">DMA4_DMA20_IRQHandler</a>
 <LI><a href="#[f]">DMA5_DMA21_IRQHandler</a>
 <LI><a href="#[10]">DMA6_DMA22_IRQHandler</a>
 <LI><a href="#[11]">DMA7_DMA23_IRQHandler</a>
 <LI><a href="#[12]">DMA8_DMA24_IRQHandler</a>
 <LI><a href="#[13]">DMA9_DMA25_IRQHandler</a>
 <LI><a href="#[14]">DMA10_DMA26_IRQHandler</a>
 <LI><a href="#[15]">DMA11_DMA27_IRQHandler</a>
 <LI><a href="#[16]">DMA12_DMA28_IRQHandler</a>
 <LI><a href="#[17]">DMA13_DMA29_IRQHandler</a>
 <LI><a href="#[18]">DMA14_DMA30_IRQHandler</a>
 <LI><a href="#[19]">DMA15_DMA31_IRQHandler</a>
 <LI><a href="#[1a]">DMA_ERROR_IRQHandler</a>
 <LI><a href="#[24]">LPUART7_IRQHandler</a>
 <LI><a href="#[26]">LPI2C1_IRQHandler</a>
 <LI><a href="#[27]">LPI2C2_IRQHandler</a>
 <LI><a href="#[28]">LPI2C3_IRQHandler</a>
 <LI><a href="#[29]">LPI2C4_IRQHandler</a>
 <LI><a href="#[2a]">LPSPI1_IRQHandler</a>
 <LI><a href="#[2b]">LPSPI2_IRQHandler</a>
 <LI><a href="#[2c]">LPSPI3_IRQHandler</a>
 <LI><a href="#[2d]">LPSPI4_IRQHandler</a>
 <LI><a href="#[2e]">CAN1_IRQHandler</a>
 <LI><a href="#[2f]">CAN2_IRQHandler</a>
 <LI><a href="#[42]">SAI1_IRQHandler</a>
 <LI><a href="#[43]">SAI2_IRQHandler</a>
 <LI><a href="#[44]">SAI3_RX_IRQHandler</a>
 <LI><a href="#[45]">SAI3_TX_IRQHandler</a>
 <LI><a href="#[46]">SPDIF_IRQHandler</a>
 <LI><a href="#[64]">FLEXIO1_IRQHandler</a>
 <LI><a href="#[65]">FLEXIO2_IRQHandler</a>
 <LI><a href="#[75]">FLEXSPI2_IRQHandler</a>
 <LI><a href="#[76]">FLEXSPI_IRQHandler</a>
 <LI><a href="#[78]">USDHC1_IRQHandler</a>
 <LI><a href="#[79]">USDHC2_IRQHandler</a>
 <LI><a href="#[7c]">ENET_IRQHandler</a>
 <LI><a href="#[7d]">ENET_1588_Timer_IRQHandler</a>
 <LI><a href="#[a2]">ENET2_IRQHandler</a>
 <LI><a href="#[a3]">ENET2_1588_Timer_IRQHandler</a>
 <LI><a href="#[a4]">CAN3_IRQHandler</a>
 <LI><a href="#[a6]">FLEXIO3_IRQHandler</a>
</UL>
</UL>
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1cf]">pwm_set_duty</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1ba]">debug_assert_handler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[85]">ACMP1_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[86]">ACMP2_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[87]">ACMP3_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[88]">ACMP4_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[4d]">ADC1_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[4e]">ADC2_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[83]">ADC_ETC_ERROR_IRQ_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[80]">ADC_ETC_IRQ0_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[81]">ADC_ETC_IRQ1_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[82]">ADC_ETC_IRQ2_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[41]">BEE_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[4]">BusFault_Handler</a> from zf_common_vector.o(.text.BusFault_Handler) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[cd]">CAN1_DriverIRQHandler</a> from fsl_flexcan.o(.text.CAN1_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[2e]">CAN1_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[ce]">CAN2_DriverIRQHandler</a> from fsl_flexcan.o(.text.CAN2_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[2f]">CAN2_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[de]">CAN3_DriverIRQHandler</a> from fsl_flexcan.o(.text.CAN3_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[a4]">CAN3_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[69]">CCM_1_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[6a]">CCM_2_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[1d]">CORE_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[35]">CSI_IRQHandler</a> from isr.o(.text.CSI_IRQHandler) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[3b]">CSU_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[1b]">CTI0_ERROR_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[1c]">CTI1_ERROR_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[4f]">DCDC_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[3c]">DCP_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[3d]">DCP_VMI_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[ac]">DMA0_DMA16_DriverIRQHandler</a> from fsl_edma.o(.text.DMA0_DMA16_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[a]">DMA0_DMA16_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[b6]">DMA10_DMA26_DriverIRQHandler</a> from fsl_edma.o(.text.DMA10_DMA26_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[14]">DMA10_DMA26_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[b7]">DMA11_DMA27_DriverIRQHandler</a> from fsl_edma.o(.text.DMA11_DMA27_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[15]">DMA11_DMA27_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[b8]">DMA12_DMA28_DriverIRQHandler</a> from fsl_edma.o(.text.DMA12_DMA28_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[16]">DMA12_DMA28_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[b9]">DMA13_DMA29_DriverIRQHandler</a> from fsl_edma.o(.text.DMA13_DMA29_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[17]">DMA13_DMA29_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[ba]">DMA14_DMA30_DriverIRQHandler</a> from fsl_edma.o(.text.DMA14_DMA30_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[18]">DMA14_DMA30_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[bb]">DMA15_DMA31_DriverIRQHandler</a> from fsl_edma.o(.text.DMA15_DMA31_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[19]">DMA15_DMA31_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[ad]">DMA1_DMA17_DriverIRQHandler</a> from fsl_edma.o(.text.DMA1_DMA17_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[b]">DMA1_DMA17_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[ae]">DMA2_DMA18_DriverIRQHandler</a> from fsl_edma.o(.text.DMA2_DMA18_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[c]">DMA2_DMA18_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[af]">DMA3_DMA19_DriverIRQHandler</a> from fsl_edma.o(.text.DMA3_DMA19_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[d]">DMA3_DMA19_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[b0]">DMA4_DMA20_DriverIRQHandler</a> from fsl_edma.o(.text.DMA4_DMA20_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[e]">DMA4_DMA20_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[b1]">DMA5_DMA21_DriverIRQHandler</a> from fsl_edma.o(.text.DMA5_DMA21_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[f]">DMA5_DMA21_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[b2]">DMA6_DMA22_DriverIRQHandler</a> from fsl_edma.o(.text.DMA6_DMA22_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[10]">DMA6_DMA22_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[b3]">DMA7_DMA23_DriverIRQHandler</a> from fsl_edma.o(.text.DMA7_DMA23_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[11]">DMA7_DMA23_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[b4]">DMA8_DMA24_DriverIRQHandler</a> from fsl_edma.o(.text.DMA8_DMA24_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[12]">DMA8_DMA24_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[b5]">DMA9_DMA25_DriverIRQHandler</a> from fsl_edma.o(.text.DMA9_DMA25_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[13]">DMA9_DMA25_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[bc]">DMA_ERROR_DriverIRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[1a]">DMA_ERROR_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[e4]">DbgConsole_Putchar</a> from fsl_debug_console.o(.text.DbgConsole_Putchar) referenced 2 times from fsl_debug_console.o(.text.DbgConsole_Vprintf)
 <LI><a href="#[7]">DebugMon_Handler</a> from zf_common_vector.o(.text.DebugMon_Handler) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[a8]">DefaultISR</a> from startup_mimxrt1064.o(.text) referenced from fsl_csi.o(.data.s_csiIsr)
 <LI><a href="#[a8]">DefaultISR</a> from startup_mimxrt1064.o(.text) referenced from fsl_flexcan.o(.data.s_flexcanIsr)
 <LI><a href="#[a8]">DefaultISR</a> from startup_mimxrt1064.o(.text) referenced from fsl_lpuart.o(.data.s_lpuartIsr)
 <LI><a href="#[a8]">DefaultISR</a> from startup_mimxrt1064.o(.text) referenced from fsl_usdhc.o(.data.s_usdhcIsr)
 <LI><a href="#[a8]">DefaultISR</a> from startup_mimxrt1064.o(.text) referenced 81 times from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[8b]">ENC1_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[8c]">ENC2_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[8d]">ENC3_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[8e]">ENC4_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[dd]">ENET2_1588_Timer_DriverIRQHandler</a> from fsl_enet.o(.text.ENET2_1588_Timer_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[a3]">ENET2_1588_Timer_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[dc]">ENET2_DriverIRQHandler</a> from fsl_enet.o(.text.ENET2_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[a2]">ENET2_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[db]">ENET_1588_Timer_DriverIRQHandler</a> from fsl_enet.o(.text.ENET_1588_Timer_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[7d]">ENET_1588_Timer_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[da]">ENET_DriverIRQHandler</a> from fsl_enet.o(.text.ENET_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[7c]">ENET_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[68]">EWM_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[d4]">FLEXIO1_DriverIRQHandler</a> from fsl_flexio.o(.text.FLEXIO1_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[64]">FLEXIO1_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[d5]">FLEXIO2_DriverIRQHandler</a> from fsl_flexio.o(.text.FLEXIO2_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[65]">FLEXIO2_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[df]">FLEXIO3_DriverIRQHandler</a> from fsl_flexio.o(.text.FLEXIO3_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[a6]">FLEXIO3_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[30]">FLEXRAM_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[d6]">FLEXSPI2_DriverIRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[75]">FLEXSPI2_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[d7]">FLEXSPI_DriverIRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[76]">FLEXSPI_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[6b]">GPC_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[51]">GPIO10_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[5a]">GPIO1_Combined_0_15_IRQHandler</a> from isr.o(.text.GPIO1_Combined_0_15_IRQHandler) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[5b]">GPIO1_Combined_16_31_IRQHandler</a> from isr.o(.text.GPIO1_Combined_16_31_IRQHandler) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[52]">GPIO1_INT0_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[53]">GPIO1_INT1_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[54]">GPIO1_INT2_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[55]">GPIO1_INT3_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[56]">GPIO1_INT4_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[57]">GPIO1_INT5_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[58]">GPIO1_INT6_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[59]">GPIO1_INT7_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[5c]">GPIO2_Combined_0_15_IRQHandler</a> from isr.o(.text.GPIO2_Combined_0_15_IRQHandler) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[5d]">GPIO2_Combined_16_31_IRQHandler</a> from isr.o(.text.GPIO2_Combined_16_31_IRQHandler) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[5e]">GPIO3_Combined_0_15_IRQHandler</a> from isr.o(.text.GPIO3_Combined_0_15_IRQHandler) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[5f]">GPIO3_Combined_16_31_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[60]">GPIO4_Combined_0_15_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[61]">GPIO4_Combined_16_31_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[62]">GPIO5_Combined_0_15_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[63]">GPIO5_Combined_16_31_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[a7]">GPIO6_7_8_9_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[33]">GPR_IRQ_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[6e]">GPT1_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[6f]">GPT2_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[2]">HardFault_Handler</a> from zf_common_vector.o(.text.HardFault_Handler) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[2]">HardFault_Handler</a> from zf_common_vector.o(.text.HardFault_Handler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[31]">KPP_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[34]">LCDIF_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[c5]">LPI2C1_DriverIRQHandler</a> from fsl_lpi2c.o(.text.LPI2C1_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[26]">LPI2C1_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[c6]">LPI2C2_DriverIRQHandler</a> from fsl_lpi2c.o(.text.LPI2C2_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[27]">LPI2C2_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[c7]">LPI2C3_DriverIRQHandler</a> from fsl_lpi2c.o(.text.LPI2C3_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[28]">LPI2C3_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[c8]">LPI2C4_DriverIRQHandler</a> from fsl_lpi2c.o(.text.LPI2C4_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[29]">LPI2C4_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[c9]">LPSPI1_DriverIRQHandler</a> from fsl_lpspi.o(.text.LPSPI1_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[2a]">LPSPI1_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[ca]">LPSPI2_DriverIRQHandler</a> from fsl_lpspi.o(.text.LPSPI2_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[2b]">LPSPI2_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[cb]">LPSPI3_DriverIRQHandler</a> from fsl_lpspi.o(.text.LPSPI3_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[2c]">LPSPI3_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[cc]">LPSPI4_DriverIRQHandler</a> from fsl_lpspi.o(.text.LPSPI4_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[2d]">LPSPI4_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[bd]">LPUART1_DriverIRQHandler</a> from fsl_lpuart.o(.text.LPUART1_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[1e]">LPUART1_IRQHandler</a> from isr.o(.text.LPUART1_IRQHandler) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[be]">LPUART2_DriverIRQHandler</a> from fsl_lpuart.o(.text.LPUART2_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[1f]">LPUART2_IRQHandler</a> from isr.o(.text.LPUART2_IRQHandler) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[bf]">LPUART3_DriverIRQHandler</a> from fsl_lpuart.o(.text.LPUART3_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[20]">LPUART3_IRQHandler</a> from isr.o(.text.LPUART3_IRQHandler) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[c0]">LPUART4_DriverIRQHandler</a> from fsl_lpuart.o(.text.LPUART4_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[21]">LPUART4_IRQHandler</a> from isr.o(.text.LPUART4_IRQHandler) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[c1]">LPUART5_DriverIRQHandler</a> from fsl_lpuart.o(.text.LPUART5_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[22]">LPUART5_IRQHandler</a> from isr.o(.text.LPUART5_IRQHandler) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[c2]">LPUART6_DriverIRQHandler</a> from fsl_lpuart.o(.text.LPUART6_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[23]">LPUART6_IRQHandler</a> from isr.o(.text.LPUART6_IRQHandler) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[c3]">LPUART7_DriverIRQHandler</a> from fsl_lpuart.o(.text.LPUART7_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[24]">LPUART7_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[c4]">LPUART8_DriverIRQHandler</a> from fsl_lpuart.o(.text.LPUART8_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[25]">LPUART8_IRQHandler</a> from isr.o(.text.LPUART8_IRQHandler) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[3]">MemManage_Handler</a> from zf_common_vector.o(.text.MemManage_Handler) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[1]">NMI_Handler</a> from zf_common_vector.o(.text.NMI_Handler) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[1]">NMI_Handler</a> from zf_common_vector.o(.text.NMI_Handler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[84]">PIT_IRQHandler</a> from isr.o(.text.PIT_IRQHandler) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[47]">PMU_EVENT_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[70]">PWM1_0_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[71]">PWM1_1_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[72]">PWM1_2_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[73]">PWM1_3_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[74]">PWM1_FAULT_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[93]">PWM2_0_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[94]">PWM2_1_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[95]">PWM2_2_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[96]">PWM2_3_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[97]">PWM2_FAULT_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[98]">PWM3_0_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[99]">PWM3_1_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[9a]">PWM3_2_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[9b]">PWM3_3_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[9c]">PWM3_FAULT_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[9d]">PWM4_0_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[9e]">PWM4_1_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[9f]">PWM4_2_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[a0]">PWM4_3_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[a1]">PWM4_FAULT_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[36]">PXP_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[8]">PendSV_Handler</a> from zf_common_vector.o(.text.PendSV_Handler) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[8]">PendSV_Handler</a> from zf_common_vector.o(.text.PendSV_Handler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[67]">RTWDOG_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[6d]">Reserved115_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[89]">Reserved143_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[8a]">Reserved144_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[a5]">Reserved171_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[3e]">Reserved68_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[48]">Reserved78_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[50]">Reserved86_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[0]">Reset_Handler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[cf]">SAI1_DriverIRQHandler</a> from fsl_sai.o(.text.SAI1_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[42]">SAI1_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[d0]">SAI2_DriverIRQHandler</a> from fsl_sai.o(.text.SAI2_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[43]">SAI2_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[d1]">SAI3_RX_DriverIRQHandler</a> from fsl_sai.o(.text.SAI3_RX_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[44]">SAI3_RX_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[d2]">SAI3_TX_DriverIRQHandler</a> from fsl_sai.o(.text.SAI3_TX_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[45]">SAI3_TX_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[77]">SEMC_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[40]">SJC_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[38]">SNVS_HP_WRAPPER_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[39]">SNVS_HP_WRAPPER_TZ_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[3a]">SNVS_LP_WRAPPER_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[d3]">SPDIF_DriverIRQHandler</a> from fsl_spdif.o(.text.SPDIF_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[46]">SPDIF_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[6c]">SRC_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[6]">SVC_Handler</a> from zf_common_vector.o(.text.SVC_Handler) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[6]">SVC_Handler</a> from zf_common_vector.o(.text.SVC_Handler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[9]">SysTick_Handler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[9]">SysTick_Handler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[aa]">SystemInit</a> from system_mimxrt1064.o(.text.SystemInit) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[49]">TEMP_LOW_HIGH_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[4a]">TEMP_PANIC_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[8f]">TMR1_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[90]">TMR2_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[91]">TMR3_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[92]">TMR4_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[3f]">TRNG_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[32]">TSC_DIG_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[7b]">USB_OTG1_IRQHandler</a> from zf_driver_usb_cdc.o(.text.USB_OTG1_IRQHandler) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[7a]">USB_OTG2_IRQHandler</a> from zf_driver_usb_cdc.o(.text.USB_OTG2_IRQHandler) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[4b]">USB_PHY1_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[4c]">USB_PHY2_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[d8]">USDHC1_DriverIRQHandler</a> from fsl_usdhc.o(.text.USDHC1_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[78]">USDHC1_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[d9]">USDHC2_DriverIRQHandler</a> from fsl_usdhc.o(.text.USDHC2_DriverIRQHandler) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[79]">USDHC2_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[5]">UsageFault_Handler</a> from zf_common_vector.o(.text.UsageFault_Handler) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[66]">WDOG1_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[37]">WDOG2_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[7e]">XBAR1_IRQ_0_1_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[7f]">XBAR1_IRQ_2_3_IRQHandler</a> from startup_mimxrt1064.o(.text) referenced from startup_mimxrt1064.o(.isr_vector)
 <LI><a href="#[ab]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_mimxrt1064.o(.text)
 <LI><a href="#[e1]">_sbackspace</a> from _sgetc.o(.text) referenced from sscanf.o(.text)
 <LI><a href="#[e2]">_scanf_char_input</a> from scanf_char.o(.text) referenced from scanf_char.o(.text)
 <LI><a href="#[e0]">_sgetc</a> from _sgetc.o(.text) referenced from sscanf.o(.text)
 <LI><a href="#[e9]">_sputc</a> from printfa.o(i._sputc) referenced from printfa.o(i.__0sprintf)
 <LI><a href="#[e5]">debug_uart_str_output</a> from zf_common_debug.o(.text.debug_uart_str_output) referenced 2 times from zf_common_debug.o(.text.debug_init)
 <LI><a href="#[e8]">fputc</a> from zf_common_debug.o(.text.fputc) referenced from printfa.o(i.__0printf)
 <LI><a href="#[e7]">ips200_clear</a> from zf_device_ips200.o(.text.ips200_clear) referenced 2 times from zf_device_ips200.o(.text.ips200_debug_init)
 <LI><a href="#[e6]">ips200_show_string</a> from zf_device_ips200.o(.text.ips200_show_string) referenced 2 times from zf_device_ips200.o(.text.ips200_debug_init)
 <LI><a href="#[e3]">isspace</a> from isspace_c.o(.text) referenced from scanf_char.o(.text)
 <LI><a href="#[a9]">main</a> from main.o(.text.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
 <LI><a href="#[ea]">type_default_callback</a> from zf_device_type.o(.text.type_default_callback) referenced from zf_device_type.o(.data.camera_uart_handler)
 <LI><a href="#[ea]">type_default_callback</a> from zf_device_type.o(.text.type_default_callback) referenced from zf_device_type.o(.data.flexio_camera_vsync_handler)
 <LI><a href="#[ea]">type_default_callback</a> from zf_device_type.o(.text.type_default_callback) referenced from zf_device_type.o(.data.tof_module_exti_handler)
 <LI><a href="#[ea]">type_default_callback</a> from zf_device_type.o(.text.type_default_callback) referenced from zf_device_type.o(.data.wireless_module_spi_handler)
 <LI><a href="#[ea]">type_default_callback</a> from zf_device_type.o(.text.type_default_callback) referenced from zf_device_type.o(.data.wireless_module_uart_handler)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[ab]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[226]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry4.o(.ARM.Collect$$$$00000003))

<P><STRONG><a name="[222]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[224]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[227]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[228]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[229]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[22a]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[22b]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[0]"></a>Reset_Handler</STRONG> (Thumb, 104 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[85]"></a>ACMP1_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DefaultISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[86]"></a>ACMP2_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[87]"></a>ACMP3_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[88]"></a>ACMP4_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[4d]"></a>ADC1_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[4e]"></a>ADC2_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[83]"></a>ADC_ETC_ERROR_IRQ_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[80]"></a>ADC_ETC_IRQ0_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[81]"></a>ADC_ETC_IRQ1_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[82]"></a>ADC_ETC_IRQ2_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[41]"></a>BEE_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[69]"></a>CCM_1_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[6a]"></a>CCM_2_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[1d]"></a>CORE_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[3b]"></a>CSU_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[1b]"></a>CTI0_ERROR_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[1c]"></a>CTI1_ERROR_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[4f]"></a>DCDC_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[3c]"></a>DCP_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[3d]"></a>DCP_VMI_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[bc]"></a>DMA_ERROR_DriverIRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[a8]"></a>DefaultISR</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ACMP1_IRQHandler
</UL>
<BR>[Address Reference Count : 5]<UL><LI> fsl_csi.o(.data.s_csiIsr)
<LI> fsl_flexcan.o(.data.s_flexcanIsr)
<LI> fsl_lpuart.o(.data.s_lpuartIsr)
<LI> startup_mimxrt1064.o(.isr_vector)
<LI> fsl_usdhc.o(.data.s_usdhcIsr)
</UL>
<P><STRONG><a name="[8b]"></a>ENC1_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[8c]"></a>ENC2_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[8d]"></a>ENC3_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[8e]"></a>ENC4_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[68]"></a>EWM_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[30]"></a>FLEXRAM_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[d6]"></a>FLEXSPI2_DriverIRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[d7]"></a>FLEXSPI_DriverIRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[6b]"></a>GPC_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[51]"></a>GPIO10_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[52]"></a>GPIO1_INT0_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[53]"></a>GPIO1_INT1_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[54]"></a>GPIO1_INT2_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[55]"></a>GPIO1_INT3_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[56]"></a>GPIO1_INT4_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[57]"></a>GPIO1_INT5_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[58]"></a>GPIO1_INT6_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[59]"></a>GPIO1_INT7_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[5f]"></a>GPIO3_Combined_16_31_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[60]"></a>GPIO4_Combined_0_15_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[61]"></a>GPIO4_Combined_16_31_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[62]"></a>GPIO5_Combined_0_15_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[63]"></a>GPIO5_Combined_16_31_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[a7]"></a>GPIO6_7_8_9_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[33]"></a>GPR_IRQ_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[6e]"></a>GPT1_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[6f]"></a>GPT2_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[31]"></a>KPP_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[34]"></a>LCDIF_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[47]"></a>PMU_EVENT_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[70]"></a>PWM1_0_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[71]"></a>PWM1_1_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[72]"></a>PWM1_2_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[73]"></a>PWM1_3_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[74]"></a>PWM1_FAULT_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[93]"></a>PWM2_0_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[94]"></a>PWM2_1_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[95]"></a>PWM2_2_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[96]"></a>PWM2_3_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[97]"></a>PWM2_FAULT_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[98]"></a>PWM3_0_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[99]"></a>PWM3_1_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[9a]"></a>PWM3_2_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[9b]"></a>PWM3_3_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[9c]"></a>PWM3_FAULT_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[9d]"></a>PWM4_0_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[9e]"></a>PWM4_1_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[9f]"></a>PWM4_2_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[a0]"></a>PWM4_3_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[a1]"></a>PWM4_FAULT_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[36]"></a>PXP_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[67]"></a>RTWDOG_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[6d]"></a>Reserved115_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[89]"></a>Reserved143_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[8a]"></a>Reserved144_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[a5]"></a>Reserved171_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[3e]"></a>Reserved68_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[48]"></a>Reserved78_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[50]"></a>Reserved86_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[77]"></a>SEMC_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[40]"></a>SJC_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[38]"></a>SNVS_HP_WRAPPER_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[39]"></a>SNVS_HP_WRAPPER_TZ_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[3a]"></a>SNVS_LP_WRAPPER_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[6c]"></a>SRC_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[49]"></a>TEMP_LOW_HIGH_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[4a]"></a>TEMP_PANIC_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[8f]"></a>TMR1_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[90]"></a>TMR2_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[91]"></a>TMR3_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[92]"></a>TMR4_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[3f]"></a>TRNG_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[32]"></a>TSC_DIG_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[4b]"></a>USB_PHY1_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[4c]"></a>USB_PHY2_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[66]"></a>WDOG1_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[37]"></a>WDOG2_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[7e]"></a>XBAR1_IRQ_0_1_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[7f]"></a>XBAR1_IRQ_2_3_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[9]"></a>SysTick_Handler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 2]<UL><LI> startup_mimxrt1064.o(.text)
<LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[a]"></a>DMA0_DMA16_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[b]"></a>DMA1_DMA17_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[c]"></a>DMA2_DMA18_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[d]"></a>DMA3_DMA19_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[e]"></a>DMA4_DMA20_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[f]"></a>DMA5_DMA21_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[10]"></a>DMA6_DMA22_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[11]"></a>DMA7_DMA23_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[12]"></a>DMA8_DMA24_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[13]"></a>DMA9_DMA25_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[14]"></a>DMA10_DMA26_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[15]"></a>DMA11_DMA27_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[16]"></a>DMA12_DMA28_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[17]"></a>DMA13_DMA29_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[18]"></a>DMA14_DMA30_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[19]"></a>DMA15_DMA31_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[1a]"></a>DMA_ERROR_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[24]"></a>LPUART7_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[26]"></a>LPI2C1_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[27]"></a>LPI2C2_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[28]"></a>LPI2C3_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[29]"></a>LPI2C4_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[2a]"></a>LPSPI1_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[2b]"></a>LPSPI2_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[2c]"></a>LPSPI3_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[2d]"></a>LPSPI4_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[2e]"></a>CAN1_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[2f]"></a>CAN2_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[42]"></a>SAI1_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[43]"></a>SAI2_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[44]"></a>SAI3_RX_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[45]"></a>SAI3_TX_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[46]"></a>SPDIF_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[64]"></a>FLEXIO1_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[65]"></a>FLEXIO2_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[75]"></a>FLEXSPI2_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[76]"></a>FLEXSPI_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[78]"></a>USDHC1_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[79]"></a>USDHC2_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[7c]"></a>ENET_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[7d]"></a>ENET_1588_Timer_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[a2]"></a>ENET2_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[a3]"></a>ENET2_1588_Timer_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[a4]"></a>CAN3_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[a6]"></a>FLEXIO3_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size unknown bytes, startup_mimxrt1064.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[223]"></a>__scatterload</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[22c]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[aa]"></a>SystemInit</STRONG> (Thumb, 420 bytes, Stack size 24 bytes, system_mimxrt1064.o(.text.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SystemInit
</UL>
<BR>[Calls]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInitHook
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[225]"></a>SystemInitHook</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, system_mimxrt1064.o(.text.SystemInitHook))
<BR><BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[22d]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[22e]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[22f]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[eb]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterSetDelayTimes
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DbgConsole_ConvertRadixNumToString
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ldivmod
</UL>

<P><STRONG><a name="[ee]"></a>__aeabi_ldivmod</STRONG> (Thumb, 98 bytes, Stack size 24 bytes, ldiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = __aeabi_ldivmod &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DbgConsole_ConvertRadixNumToString
</UL>

<P><STRONG><a name="[230]"></a>___aeabi_memcpy4$ret</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, memcpy.o(.text), UNUSED)

<P><STRONG><a name="[231]"></a>___aeabi_memcpy8$ret</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, memcpy.o(.text), UNUSED)

<P><STRONG><a name="[232]"></a>__aeabi_memcpy</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, memcpy.o(.text), UNUSED)

<P><STRONG><a name="[15d]"></a>memcpy</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, memcpy.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fifo_write_buffer
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DbgConsole_PrintfFormattedData
</UL>

<P><STRONG><a name="[f0]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memset
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[233]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[234]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[ef]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[235]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[236]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[f1]"></a>memset</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = memset
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_GetDefaultConfig
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPT_GetDefaultConfig
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_GetDefaultConfig
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_output
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fifo_clear
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_uart_callback
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_show_float
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_show_int
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterGetDefaultConfig
</UL>

<P><STRONG><a name="[15e]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_output
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_value_float
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_value_int
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DbgConsole_PrintfFormattedData
</UL>

<P><STRONG><a name="[f2]"></a>sscanf</STRONG> (Thumb, 48 bytes, Stack size 72 bytes, sscanf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = sscanf &rArr; __vfscanf_char &rArr; __vfscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf_char
</UL>
<BR>[Called By]<UL><LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_uart_callback
</UL>

<P><STRONG><a name="[237]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[221]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[ed]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[238]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[ec]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[239]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[f4]"></a>_scanf_longlong</STRONG> (Thumb, 342 bytes, Stack size 56 bytes, _scanf_longlong.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _scanf_longlong
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_chval
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>

<P><STRONG><a name="[f6]"></a>_scanf_int</STRONG> (Thumb, 332 bytes, Stack size 56 bytes, _scanf_int.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _scanf_int
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_chval
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>

<P><STRONG><a name="[108]"></a>_scanf_string</STRONG> (Thumb, 224 bytes, Stack size 56 bytes, _scanf_str.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _scanf_string
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>

<P><STRONG><a name="[107]"></a>_scanf_real</STRONG> (Thumb, 0 bytes, Stack size 104 bytes, scanf_fp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = _scanf_real
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>

<P><STRONG><a name="[fb]"></a>_scanf_really_real</STRONG> (Thumb, 556 bytes, Stack size 104 bytes, scanf_fp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_is_digit
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
</UL>

<P><STRONG><a name="[f3]"></a>__vfscanf_char</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, scanf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = __vfscanf_char &rArr; __vfscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sscanf
</UL>

<P><STRONG><a name="[e0]"></a>_sgetc</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> sscanf.o(.text)
</UL>
<P><STRONG><a name="[e1]"></a>_sbackspace</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> sscanf.o(.text)
</UL>
<P><STRONG><a name="[23a]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 48 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[ff]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[103]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[104]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[f9]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[fa]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[21b]"></a>__ARM_scalbn</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, dscalb.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __ARM_scalbn
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[23b]"></a>scalbn</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, dscalb.o(.text), UNUSED)

<P><STRONG><a name="[105]"></a>__aeabi_d2ulz</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[21e]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[100]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[23c]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[e3]"></a>isspace</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, isspace_c.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ctype_lookup
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scanf_char.o(.text)
</UL>
<P><STRONG><a name="[f5]"></a>_chval</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, _chval.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_int
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_longlong
</UL>

<P><STRONG><a name="[fe]"></a>__vfscanf</STRONG> (Thumb, 808 bytes, Stack size 88 bytes, _scanf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = __vfscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_real
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_string
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_int
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_longlong
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf_char
</UL>

<P><STRONG><a name="[102]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[101]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ul2d
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[f8]"></a>__aeabi_ul2d</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, dfltul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
</UL>

<P><STRONG><a name="[fd]"></a>__aeabi_d2f</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, d2f.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[106]"></a>__ctype_lookup</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, ctype_c.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;isspace
</UL>

<P><STRONG><a name="[109]"></a>_float_round</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, fepilogue.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>

<P><STRONG><a name="[23d]"></a>_float_epilogue</STRONG> (Thumb, 92 bytes, Stack size 4 bytes, fepilogue.o(.text), UNUSED)

<P><STRONG><a name="[10a]"></a>ADC_DoAutoCalibration</STRONG> (Thumb, 166 bytes, Stack size 24 bytes, fsl_adc.o(.text.ADC_DoAutoCalibration))
<BR><BR>[Stack]<UL><LI>Max Depth = 624<LI>Call Chain = ADC_DoAutoCalibration &rArr; ADC_GetChannelConversionValue &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_GetChannelConversionValue
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_GetChannelStatusFlags
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_GetStatusFlags
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_EnableHardwareTrigger
</UL>
<BR>[Called By]<UL><LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_init
</UL>

<P><STRONG><a name="[110]"></a>ADC_GetDefaultConfig</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, fsl_adc.o(.text.ADC_GetDefaultConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = ADC_GetDefaultConfig &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memset
</UL>
<BR>[Called By]<UL><LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_init
</UL>

<P><STRONG><a name="[112]"></a>ADC_Init</STRONG> (Thumb, 274 bytes, Stack size 24 bytes, fsl_adc.o(.text.ADC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 656<LI>Call Chain = ADC_Init &rArr; CLOCK_EnableClock &rArr; CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_EnableClock
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_GetInstance
</UL>
<BR>[Called By]<UL><LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_init
</UL>

<P><STRONG><a name="[114]"></a>BOARD_BootClockRUN</STRONG> (Thumb, 1494 bytes, Stack size 48 bytes, clock_config.o(.text.BOARD_BootClockRUN))
<BR><BR>[Stack]<UL><LI>Max Depth = 680<LI>Call Chain = BOARD_BootClockRUN &rArr; CLOCK_DisableClock &rArr; CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_DeinitEnetPll
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_DeinitVideoPll
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_DeinitAudioPll
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_DeinitUsb2Pll
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_InitArmPll
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_InitRcOsc24M
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_SwitchOsc
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_InitExternalClk
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_DisableClock
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_SetDiv
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_SetPllBypass
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_SetMux
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_SetXtalFreq
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_SetRtcXtalFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BOARD_InitBootClocks
</UL>

<P><STRONG><a name="[123]"></a>BOARD_ConfigMPU</STRONG> (Thumb, 598 bytes, Stack size 40 bytes, board.o(.text.BOARD_ConfigMPU))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = BOARD_ConfigMPU &rArr; ARM_MPU_Enable
</UL>
<BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ARM_MPU_Enable
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ARM_MPU_Disable
</UL>
<BR>[Called By]<UL><LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clock_init
</UL>

<P><STRONG><a name="[126]"></a>BOARD_DebugConsoleSrcFreq</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, board.o(.text.BOARD_DebugConsoleSrcFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 672<LI>Call Chain = BOARD_DebugConsoleSrcFreq &rArr; CLOCK_GetPllFreq &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetPllFreq
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetOscFreq
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetDiv
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetMux
</UL>
<BR>[Called By]<UL><LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_init
</UL>

<P><STRONG><a name="[12b]"></a>BOARD_InitBootClocks</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, clock_config.o(.text.BOARD_InitBootClocks))
<BR><BR>[Stack]<UL><LI>Max Depth = 688<LI>Call Chain = BOARD_InitBootClocks &rArr; BOARD_BootClockRUN &rArr; CLOCK_DisableClock &rArr; CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BOARD_BootClockRUN
</UL>
<BR>[Called By]<UL><LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clock_init
</UL>

<P><STRONG><a name="[4]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, zf_common_vector.o(.text.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[cd]"></a>CAN1_DriverIRQHandler</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, fsl_flexcan.o(.text.CAN1_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 600<LI>Call Chain = CAN1_DriverIRQHandler &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[ce]"></a>CAN2_DriverIRQHandler</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, fsl_flexcan.o(.text.CAN2_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 600<LI>Call Chain = CAN2_DriverIRQHandler &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[de]"></a>CAN3_DriverIRQHandler</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, fsl_flexcan.o(.text.CAN3_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 600<LI>Call Chain = CAN3_DriverIRQHandler &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[11c]"></a>CLOCK_DeinitAudioPll</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, fsl_clock.o(.text.CLOCK_DeinitAudioPll))
<BR><BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BOARD_BootClockRUN
</UL>

<P><STRONG><a name="[11f]"></a>CLOCK_DeinitEnetPll</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, fsl_clock.o(.text.CLOCK_DeinitEnetPll))
<BR><BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BOARD_BootClockRUN
</UL>

<P><STRONG><a name="[120]"></a>CLOCK_DeinitUsb2Pll</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, fsl_clock.o(.text.CLOCK_DeinitUsb2Pll))
<BR><BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BOARD_BootClockRUN
</UL>

<P><STRONG><a name="[11e]"></a>CLOCK_DeinitVideoPll</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, fsl_clock.o(.text.CLOCK_DeinitVideoPll))
<BR><BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BOARD_BootClockRUN
</UL>

<P><STRONG><a name="[142]"></a>CLOCK_GetAhbFreq</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, fsl_clock.o(.text.CLOCK_GetAhbFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 704<LI>Call Chain = CLOCK_GetAhbFreq &rArr; CLOCK_GetPeriphClkFreq &rArr; CLOCK_GetSysPfdFreq &rArr; CLOCK_GetPllFreq &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetPeriphClkFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetFreq
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetIpgFreq
</UL>

<P><STRONG><a name="[144]"></a>CLOCK_GetFreq</STRONG> (Thumb, 368 bytes, Stack size 24 bytes, fsl_clock.o(.text.CLOCK_GetFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 752<LI>Call Chain = CLOCK_GetFreq &rArr; CLOCK_GetPerClkFreq &rArr; CLOCK_GetIpgFreq &rArr; CLOCK_GetAhbFreq &rArr; CLOCK_GetPeriphClkFreq &rArr; CLOCK_GetSysPfdFreq &rArr; CLOCK_GetPllFreq &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetPerClkFreq
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetIpgFreq
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetUsb1PfdFreq
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetSemcFreq
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetSysPfdFreq
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetPllFreq
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetAhbFreq
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetPllUsb1SWFreq
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetRtcFreq
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetOscFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;global_init
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
</UL>

<P><STRONG><a name="[146]"></a>CLOCK_GetIpgFreq</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, fsl_clock.o(.text.CLOCK_GetIpgFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 712<LI>Call Chain = CLOCK_GetIpgFreq &rArr; CLOCK_GetAhbFreq &rArr; CLOCK_GetPeriphClkFreq &rArr; CLOCK_GetSysPfdFreq &rArr; CLOCK_GetPllFreq &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetAhbFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetFreq
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetPerClkFreq
</UL>

<P><STRONG><a name="[147]"></a>CLOCK_GetPerClkFreq</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, fsl_clock.o(.text.CLOCK_GetPerClkFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 728<LI>Call Chain = CLOCK_GetPerClkFreq &rArr; CLOCK_GetIpgFreq &rArr; CLOCK_GetAhbFreq &rArr; CLOCK_GetPeriphClkFreq &rArr; CLOCK_GetSysPfdFreq &rArr; CLOCK_GetPllFreq &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetIpgFreq
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetOscFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetFreq
</UL>

<P><STRONG><a name="[128]"></a>CLOCK_GetPllFreq</STRONG> (Thumb, 1048 bytes, Stack size 56 bytes, fsl_clock.o(.text.CLOCK_GetPllFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 648<LI>Call Chain = CLOCK_GetPllFreq &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_IsPllBypassed
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetPllBypassRefClk
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_IsPllEnabled
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BOARD_DebugConsoleSrcFreq
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetFreq
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetUsb1PfdFreq
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetSysPfdFreq
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetPllUsb1SWFreq
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetPeriphClkFreq
</UL>

<P><STRONG><a name="[145]"></a>CLOCK_GetSemcFreq</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, fsl_clock.o(.text.CLOCK_GetSemcFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 712<LI>Call Chain = CLOCK_GetSemcFreq &rArr; CLOCK_GetPeriphClkFreq &rArr; CLOCK_GetSysPfdFreq &rArr; CLOCK_GetPllFreq &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetUsb1PfdFreq
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetSysPfdFreq
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetPeriphClkFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetFreq
</UL>

<P><STRONG><a name="[14c]"></a>CLOCK_GetSysPfdFreq</STRONG> (Thumb, 160 bytes, Stack size 24 bytes, fsl_clock.o(.text.CLOCK_GetSysPfdFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 672<LI>Call Chain = CLOCK_GetSysPfdFreq &rArr; CLOCK_GetPllFreq &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetPllFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetFreq
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetSemcFreq
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetPeriphClkFreq
</UL>

<P><STRONG><a name="[14a]"></a>CLOCK_GetUsb1PfdFreq</STRONG> (Thumb, 160 bytes, Stack size 24 bytes, fsl_clock.o(.text.CLOCK_GetUsb1PfdFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 672<LI>Call Chain = CLOCK_GetUsb1PfdFreq &rArr; CLOCK_GetPllFreq &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetPllFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetFreq
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetSemcFreq
</UL>

<P><STRONG><a name="[11b]"></a>CLOCK_InitArmPll</STRONG> (Thumb, 98 bytes, Stack size 4 bytes, fsl_clock.o(.text.CLOCK_InitArmPll))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = CLOCK_InitArmPll
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BOARD_BootClockRUN
</UL>

<P><STRONG><a name="[117]"></a>CLOCK_InitExternalClk</STRONG> (Thumb, 130 bytes, Stack size 8 bytes, fsl_clock.o(.text.CLOCK_InitExternalClk))
<BR><BR>[Stack]<UL><LI>Max Depth = 600<LI>Call Chain = CLOCK_InitExternalClk &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BOARD_BootClockRUN
</UL>

<P><STRONG><a name="[118]"></a>CLOCK_InitRcOsc24M</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, fsl_clock.o(.text.CLOCK_InitRcOsc24M))
<BR><BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BOARD_BootClockRUN
</UL>

<P><STRONG><a name="[119]"></a>CLOCK_SwitchOsc</STRONG> (Thumb, 46 bytes, Stack size 4 bytes, fsl_clock.o(.text.CLOCK_SwitchOsc))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = CLOCK_SwitchOsc
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BOARD_BootClockRUN
</UL>

<P><STRONG><a name="[152]"></a>CSI_DriverIRQHandler</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, fsl_csi.o(.text.CSI_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CSI_DriverIRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CSI_IRQHandler
</UL>

<P><STRONG><a name="[35]"></a>CSI_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, isr.o(.text.CSI_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = CSI_IRQHandler &rArr; CSI_DriverIRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CSI_DriverIRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[ac]"></a>DMA0_DMA16_DriverIRQHandler</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, fsl_edma.o(.text.DMA0_DMA16_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 640<LI>Call Chain = DMA0_DMA16_DriverIRQHandler &rArr; EDMA_HandleIRQ &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_HandleIRQ
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_GetChannelStatusFlags
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[b6]"></a>DMA10_DMA26_DriverIRQHandler</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, fsl_edma.o(.text.DMA10_DMA26_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 640<LI>Call Chain = DMA10_DMA26_DriverIRQHandler &rArr; EDMA_HandleIRQ &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_HandleIRQ
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_GetChannelStatusFlags
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[b7]"></a>DMA11_DMA27_DriverIRQHandler</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, fsl_edma.o(.text.DMA11_DMA27_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 640<LI>Call Chain = DMA11_DMA27_DriverIRQHandler &rArr; EDMA_HandleIRQ &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_HandleIRQ
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_GetChannelStatusFlags
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[b8]"></a>DMA12_DMA28_DriverIRQHandler</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, fsl_edma.o(.text.DMA12_DMA28_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 640<LI>Call Chain = DMA12_DMA28_DriverIRQHandler &rArr; EDMA_HandleIRQ &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_HandleIRQ
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_GetChannelStatusFlags
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[b9]"></a>DMA13_DMA29_DriverIRQHandler</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, fsl_edma.o(.text.DMA13_DMA29_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 640<LI>Call Chain = DMA13_DMA29_DriverIRQHandler &rArr; EDMA_HandleIRQ &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_HandleIRQ
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_GetChannelStatusFlags
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[ba]"></a>DMA14_DMA30_DriverIRQHandler</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, fsl_edma.o(.text.DMA14_DMA30_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 640<LI>Call Chain = DMA14_DMA30_DriverIRQHandler &rArr; EDMA_HandleIRQ &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_HandleIRQ
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_GetChannelStatusFlags
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[bb]"></a>DMA15_DMA31_DriverIRQHandler</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, fsl_edma.o(.text.DMA15_DMA31_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 640<LI>Call Chain = DMA15_DMA31_DriverIRQHandler &rArr; EDMA_HandleIRQ &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_HandleIRQ
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_GetChannelStatusFlags
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[ad]"></a>DMA1_DMA17_DriverIRQHandler</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, fsl_edma.o(.text.DMA1_DMA17_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 640<LI>Call Chain = DMA1_DMA17_DriverIRQHandler &rArr; EDMA_HandleIRQ &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_HandleIRQ
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_GetChannelStatusFlags
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[ae]"></a>DMA2_DMA18_DriverIRQHandler</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, fsl_edma.o(.text.DMA2_DMA18_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 640<LI>Call Chain = DMA2_DMA18_DriverIRQHandler &rArr; EDMA_HandleIRQ &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_HandleIRQ
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_GetChannelStatusFlags
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[af]"></a>DMA3_DMA19_DriverIRQHandler</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, fsl_edma.o(.text.DMA3_DMA19_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 640<LI>Call Chain = DMA3_DMA19_DriverIRQHandler &rArr; EDMA_HandleIRQ &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_HandleIRQ
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_GetChannelStatusFlags
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[b0]"></a>DMA4_DMA20_DriverIRQHandler</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, fsl_edma.o(.text.DMA4_DMA20_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 640<LI>Call Chain = DMA4_DMA20_DriverIRQHandler &rArr; EDMA_HandleIRQ &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_HandleIRQ
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_GetChannelStatusFlags
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[b1]"></a>DMA5_DMA21_DriverIRQHandler</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, fsl_edma.o(.text.DMA5_DMA21_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 640<LI>Call Chain = DMA5_DMA21_DriverIRQHandler &rArr; EDMA_HandleIRQ &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_HandleIRQ
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_GetChannelStatusFlags
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[b2]"></a>DMA6_DMA22_DriverIRQHandler</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, fsl_edma.o(.text.DMA6_DMA22_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 640<LI>Call Chain = DMA6_DMA22_DriverIRQHandler &rArr; EDMA_HandleIRQ &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_HandleIRQ
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_GetChannelStatusFlags
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[b3]"></a>DMA7_DMA23_DriverIRQHandler</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, fsl_edma.o(.text.DMA7_DMA23_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 640<LI>Call Chain = DMA7_DMA23_DriverIRQHandler &rArr; EDMA_HandleIRQ &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_HandleIRQ
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_GetChannelStatusFlags
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[b4]"></a>DMA8_DMA24_DriverIRQHandler</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, fsl_edma.o(.text.DMA8_DMA24_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 640<LI>Call Chain = DMA8_DMA24_DriverIRQHandler &rArr; EDMA_HandleIRQ &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_HandleIRQ
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_GetChannelStatusFlags
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[b5]"></a>DMA9_DMA25_DriverIRQHandler</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, fsl_edma.o(.text.DMA9_DMA25_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 640<LI>Call Chain = DMA9_DMA25_DriverIRQHandler &rArr; EDMA_HandleIRQ &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_HandleIRQ
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_GetChannelStatusFlags
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[159]"></a>DbgConsole_Printf</STRONG> (Thumb, 44 bytes, Stack size 32 bytes, fsl_debug_console.o(.text.DbgConsole_Printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 576<LI>Call Chain = DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DbgConsole_Vprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>

<P><STRONG><a name="[e4]"></a>DbgConsole_Putchar</STRONG> (Thumb, 58 bytes, Stack size 24 bytes, fsl_debug_console.o(.text.DbgConsole_Putchar))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = DbgConsole_Putchar
</UL>
<BR>[Address Reference Count : 1]<UL><LI> fsl_debug_console.o(.text.DbgConsole_Vprintf)
</UL>
<P><STRONG><a name="[15a]"></a>DbgConsole_Vprintf</STRONG> (Thumb, 64 bytes, Stack size 24 bytes, fsl_debug_console.o(.text.DbgConsole_Vprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 544<LI>Call Chain = DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DbgConsole_PrintfFormattedData
</UL>
<BR>[Called By]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DbgConsole_Printf
</UL>

<P><STRONG><a name="[7]"></a>DebugMon_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, zf_common_vector.o(.text.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[153]"></a>EDMA_GetChannelStatusFlags</STRONG> (Thumb, 114 bytes, Stack size 16 bytes, fsl_edma.o(.text.EDMA_GetChannelStatusFlags))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = EDMA_GetChannelStatusFlags &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA15_DMA31_DriverIRQHandler
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA14_DMA30_DriverIRQHandler
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA13_DMA29_DriverIRQHandler
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA12_DMA28_DriverIRQHandler
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA11_DMA27_DriverIRQHandler
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA10_DMA26_DriverIRQHandler
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA9_DMA25_DriverIRQHandler
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA8_DMA24_DriverIRQHandler
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA7_DMA23_DriverIRQHandler
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA6_DMA22_DriverIRQHandler
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA5_DMA21_DriverIRQHandler
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA4_DMA20_DriverIRQHandler
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA3_DMA19_DriverIRQHandler
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_DMA18_DriverIRQHandler
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_DMA17_DriverIRQHandler
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA0_DMA16_DriverIRQHandler
</UL>

<P><STRONG><a name="[154]"></a>EDMA_HandleIRQ</STRONG> (Thumb, 452 bytes, Stack size 40 bytes, fsl_edma.o(.text.EDMA_HandleIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 632<LI>Call Chain = EDMA_HandleIRQ &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA15_DMA31_DriverIRQHandler
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA14_DMA30_DriverIRQHandler
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA13_DMA29_DriverIRQHandler
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA12_DMA28_DriverIRQHandler
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA11_DMA27_DriverIRQHandler
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA10_DMA26_DriverIRQHandler
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA9_DMA25_DriverIRQHandler
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA8_DMA24_DriverIRQHandler
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA7_DMA23_DriverIRQHandler
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA6_DMA22_DriverIRQHandler
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA5_DMA21_DriverIRQHandler
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA4_DMA20_DriverIRQHandler
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA3_DMA19_DriverIRQHandler
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_DMA18_DriverIRQHandler
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_DMA17_DriverIRQHandler
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA0_DMA16_DriverIRQHandler
</UL>

<P><STRONG><a name="[dd]"></a>ENET2_1588_Timer_DriverIRQHandler</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, fsl_enet.o(.text.ENET2_1588_Timer_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 624<LI>Call Chain = ENET2_1588_Timer_DriverIRQHandler &rArr; ENET_Ptp1588IRQHandler &rArr; ENET_GetInstance &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ENET_Ptp1588IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[dc]"></a>ENET2_DriverIRQHandler</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, fsl_enet.o(.text.ENET2_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 632<LI>Call Chain = ENET2_DriverIRQHandler &rArr; ENET_CommonFrame0IRQHandler &rArr; ENET_GetInstance &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ENET_CommonFrame0IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[db]"></a>ENET_1588_Timer_DriverIRQHandler</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, fsl_enet.o(.text.ENET_1588_Timer_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 624<LI>Call Chain = ENET_1588_Timer_DriverIRQHandler &rArr; ENET_Ptp1588IRQHandler &rArr; ENET_GetInstance &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ENET_Ptp1588IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[160]"></a>ENET_CommonFrame0IRQHandler</STRONG> (Thumb, 284 bytes, Stack size 24 bytes, fsl_enet.o(.text.ENET_CommonFrame0IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 624<LI>Call Chain = ENET_CommonFrame0IRQHandler &rArr; ENET_GetInstance &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ENET_GetInstance
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ENET2_DriverIRQHandler
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ENET_DriverIRQHandler
</UL>

<P><STRONG><a name="[da]"></a>ENET_DriverIRQHandler</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, fsl_enet.o(.text.ENET_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 632<LI>Call Chain = ENET_DriverIRQHandler &rArr; ENET_CommonFrame0IRQHandler &rArr; ENET_GetInstance &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ENET_CommonFrame0IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[161]"></a>ENET_GetInstance</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, fsl_enet.o(.text.ENET_GetInstance))
<BR><BR>[Stack]<UL><LI>Max Depth = 600<LI>Call Chain = ENET_GetInstance &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ENET_Ptp1588IRQHandler
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ENET_CommonFrame0IRQHandler
</UL>

<P><STRONG><a name="[15f]"></a>ENET_Ptp1588IRQHandler</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, fsl_enet.o(.text.ENET_Ptp1588IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = ENET_Ptp1588IRQHandler &rArr; ENET_GetInstance &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ENET_GetInstance
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ENET2_1588_Timer_DriverIRQHandler
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ENET_1588_Timer_DriverIRQHandler
</UL>

<P><STRONG><a name="[d4]"></a>FLEXIO1_DriverIRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, fsl_flexio.o(.text.FLEXIO1_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = FLEXIO1_DriverIRQHandler &rArr; FLEXIO_CommonIRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLEXIO_CommonIRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[d5]"></a>FLEXIO2_DriverIRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, fsl_flexio.o(.text.FLEXIO2_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = FLEXIO2_DriverIRQHandler &rArr; FLEXIO_CommonIRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLEXIO_CommonIRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[df]"></a>FLEXIO3_DriverIRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, fsl_flexio.o(.text.FLEXIO3_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = FLEXIO3_DriverIRQHandler &rArr; FLEXIO_CommonIRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLEXIO_CommonIRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[5a]"></a>GPIO1_Combined_0_15_IRQHandler</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, isr.o(.text.GPIO1_Combined_0_15_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = GPIO1_Combined_0_15_IRQHandler &rArr; GPIO_ClearPinsInterruptFlags &rArr; GPIO_PortClearInterruptFlags
</UL>
<BR>[Calls]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ClearPinsInterruptFlags
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_GetPinsInterruptFlags
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[5b]"></a>GPIO1_Combined_16_31_IRQHandler</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, isr.o(.text.GPIO1_Combined_16_31_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = GPIO1_Combined_16_31_IRQHandler &rArr; GPIO_ClearPinsInterruptFlags &rArr; GPIO_PortClearInterruptFlags
</UL>
<BR>[Calls]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ClearPinsInterruptFlags
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_GetPinsInterruptFlags
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[5c]"></a>GPIO2_Combined_0_15_IRQHandler</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, isr.o(.text.GPIO2_Combined_0_15_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = GPIO2_Combined_0_15_IRQHandler &rArr; GPIO_ClearPinsInterruptFlags &rArr; GPIO_PortClearInterruptFlags
</UL>
<BR>[Calls]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ClearPinsInterruptFlags
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_GetPinsInterruptFlags
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[5d]"></a>GPIO2_Combined_16_31_IRQHandler</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, isr.o(.text.GPIO2_Combined_16_31_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = GPIO2_Combined_16_31_IRQHandler &rArr; GPIO_ClearPinsInterruptFlags &rArr; GPIO_PortClearInterruptFlags
</UL>
<BR>[Calls]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ClearPinsInterruptFlags
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_GetPinsInterruptFlags
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[5e]"></a>GPIO3_Combined_0_15_IRQHandler</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, isr.o(.text.GPIO3_Combined_0_15_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = GPIO3_Combined_0_15_IRQHandler &rArr; GPIO_ClearPinsInterruptFlags &rArr; GPIO_PortClearInterruptFlags
</UL>
<BR>[Calls]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ClearPinsInterruptFlags
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_GetPinsInterruptFlags
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[16e]"></a>GPIO_PinInit</STRONG> (Thumb, 154 bytes, Stack size 24 bytes, fsl_gpio.o(.text.GPIO_PinInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 656<LI>Call Chain = GPIO_PinInit &rArr; CLOCK_EnableClock &rArr; CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinWrite
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetPinInterruptConfig
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_EnableClock
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_GetInstance
</UL>
<BR>[Called By]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_init
</UL>

<P><STRONG><a name="[172]"></a>GPIO_PinSetInterruptConfig</STRONG> (Thumb, 202 bytes, Stack size 24 bytes, fsl_gpio.o(.text.GPIO_PinSetInterruptConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = GPIO_PinSetInterruptConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetPinInterruptConfig
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetPinInterruptConfig
</UL>

<P><STRONG><a name="[16f]"></a>GPIO_PinWrite</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, fsl_gpio.o(.text.GPIO_PinWrite))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = GPIO_PinWrite &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Called By]<UL><LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fast_gpio_init
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinInit
</UL>

<P><STRONG><a name="[175]"></a>GPT_Deinit</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, fsl_gpt.o(.text.GPT_Deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 648<LI>Call Chain = GPT_Deinit &rArr; CLOCK_DisableClock &rArr; CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_DisableClock
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPT_GetInstance
</UL>
<BR>[Called By]<UL><LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_delay_init
</UL>

<P><STRONG><a name="[177]"></a>GPT_GetDefaultConfig</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, fsl_gpt.o(.text.GPT_GetDefaultConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = GPT_GetDefaultConfig &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memset
</UL>
<BR>[Called By]<UL><LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_delay_init
</UL>

<P><STRONG><a name="[178]"></a>GPT_Init</STRONG> (Thumb, 160 bytes, Stack size 16 bytes, fsl_gpt.o(.text.GPT_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 648<LI>Call Chain = GPT_Init &rArr; CLOCK_EnableClock &rArr; CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPT_SetClockDivider
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPT_SetClockSource
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPT_SoftwareReset
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_EnableClock
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPT_GetInstance
</UL>
<BR>[Called By]<UL><LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_delay_init
</UL>

<P><STRONG><a name="[2]"></a>HardFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, zf_common_vector.o(.text.HardFault_Handler))
<BR>[Address Reference Count : 2]<UL><LI> startup_mimxrt1064.o(.text)
<LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[c5]"></a>LPI2C1_DriverIRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, fsl_lpi2c.o(.text.LPI2C1_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = LPI2C1_DriverIRQHandler &rArr; LPI2C_CommonIRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPI2C_CommonIRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[c6]"></a>LPI2C2_DriverIRQHandler</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, fsl_lpi2c.o(.text.LPI2C2_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = LPI2C2_DriverIRQHandler &rArr; LPI2C_CommonIRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPI2C_CommonIRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[c7]"></a>LPI2C3_DriverIRQHandler</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, fsl_lpi2c.o(.text.LPI2C3_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = LPI2C3_DriverIRQHandler &rArr; LPI2C_CommonIRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPI2C_CommonIRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[c8]"></a>LPI2C4_DriverIRQHandler</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, fsl_lpi2c.o(.text.LPI2C4_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = LPI2C4_DriverIRQHandler &rArr; LPI2C_CommonIRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPI2C_CommonIRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[c9]"></a>LPSPI1_DriverIRQHandler</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, fsl_lpspi.o(.text.LPSPI1_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 600<LI>Call Chain = LPSPI1_DriverIRQHandler &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_CommonIRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[ca]"></a>LPSPI2_DriverIRQHandler</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, fsl_lpspi.o(.text.LPSPI2_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 600<LI>Call Chain = LPSPI2_DriverIRQHandler &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_CommonIRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[cb]"></a>LPSPI3_DriverIRQHandler</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, fsl_lpspi.o(.text.LPSPI3_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 600<LI>Call Chain = LPSPI3_DriverIRQHandler &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_CommonIRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[cc]"></a>LPSPI4_DriverIRQHandler</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, fsl_lpspi.o(.text.LPSPI4_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 600<LI>Call Chain = LPSPI4_DriverIRQHandler &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_CommonIRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[17f]"></a>LPSPI_CheckTransferArgument</STRONG> (Thumb, 322 bytes, Stack size 32 bytes, fsl_lpspi.o(.text.LPSPI_CheckTransferArgument))
<BR><BR>[Stack]<UL><LI>Max Depth = 624<LI>Call Chain = LPSPI_CheckTransferArgument &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterTransferBlocking
</UL>

<P><STRONG><a name="[182]"></a>LPSPI_GetInstance</STRONG> (Thumb, 108 bytes, Stack size 8 bytes, fsl_lpspi.o(.text.LPSPI_GetInstance))
<BR><BR>[Stack]<UL><LI>Max Depth = 600<LI>Call Chain = LPSPI_GetInstance &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterInit
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterTransferBlocking
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_SetDummyData
</UL>

<P><STRONG><a name="[183]"></a>LPSPI_MasterGetDefaultConfig</STRONG> (Thumb, 146 bytes, Stack size 16 bytes, fsl_lpspi.o(.text.LPSPI_MasterGetDefaultConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = LPSPI_MasterGetDefaultConfig &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memset
</UL>
<BR>[Called By]<UL><LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
</UL>

<P><STRONG><a name="[184]"></a>LPSPI_MasterInit</STRONG> (Thumb, 284 bytes, Stack size 40 bytes, fsl_lpspi.o(.text.LPSPI_MasterInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 760<LI>Call Chain = LPSPI_MasterInit &rArr; LPSPI_MasterSetDelayTimes &rArr; LPSPI_MasterSetDelayScaler &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterSetDelayTimes
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterSetBaudRate
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_SetDummyData
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_GetInstance
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_Enable
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_SetFifoWatermarks
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_SetOnePcsPolarity
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_SetMasterSlaveMode
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_EnableClock
</UL>
<BR>[Called By]<UL><LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
</UL>

<P><STRONG><a name="[187]"></a>LPSPI_MasterSetBaudRate</STRONG> (Thumb, 260 bytes, Stack size 64 bytes, fsl_lpspi.o(.text.LPSPI_MasterSetBaudRate))
<BR><BR>[Stack]<UL><LI>Max Depth = 656<LI>Call Chain = LPSPI_MasterSetBaudRate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_IsMaster
</UL>
<BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterInit
</UL>

<P><STRONG><a name="[18c]"></a>LPSPI_MasterSetDelayScaler</STRONG> (Thumb, 122 bytes, Stack size 16 bytes, fsl_lpspi.o(.text.LPSPI_MasterSetDelayScaler))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = LPSPI_MasterSetDelayScaler &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Called By]<UL><LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterSetDelayTimes
</UL>

<P><STRONG><a name="[18a]"></a>LPSPI_MasterSetDelayTimes</STRONG> (Thumb, 478 bytes, Stack size 112 bytes, fsl_lpspi.o(.text.LPSPI_MasterSetDelayTimes))
<BR><BR>[Stack]<UL><LI>Max Depth = 720<LI>Call Chain = LPSPI_MasterSetDelayTimes &rArr; LPSPI_MasterSetDelayScaler &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterSetDelayScaler
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterInit
</UL>

<P><STRONG><a name="[18d]"></a>LPSPI_MasterTransferBlocking</STRONG> (Thumb, 1022 bytes, Stack size 104 bytes, fsl_lpspi.o(.text.LPSPI_MasterTransferBlocking))
<BR><BR>[Stack]<UL><LI>Max Depth = 728<LI>Call Chain = LPSPI_MasterTransferBlocking &rArr; LPSPI_CheckTransferArgument &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_CheckTransferArgument
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_GetInstance
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_WriteData
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_CombineWriteData
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_TxFifoReady
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_GetRxFifoSize
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_FlushFifo
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_ClearStatusFlags
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_GetStatusFlags
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_GetTxFifoCount
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_SeparateReadData
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_ReadData
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_GetRxFifoCount
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_Enable
</UL>
<BR>[Called By]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_write
</UL>

<P><STRONG><a name="[1ff]"></a>LPSPI_Reset</STRONG> (Thumb, 34 bytes, Stack size 4 bytes, fsl_lpspi.o(.text.LPSPI_Reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LPSPI_Reset
</UL>
<BR>[Called By]<UL><LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
</UL>

<P><STRONG><a name="[18b]"></a>LPSPI_SetDummyData</STRONG> (Thumb, 38 bytes, Stack size 24 bytes, fsl_lpspi.o(.text.LPSPI_SetDummyData))
<BR><BR>[Stack]<UL><LI>Max Depth = 624<LI>Call Chain = LPSPI_SetDummyData &rArr; LPSPI_GetInstance &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_GetInstance
</UL>
<BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterInit
</UL>

<P><STRONG><a name="[bd]"></a>LPUART1_DriverIRQHandler</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, fsl_lpuart.o(.text.LPUART1_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LPUART1_DriverIRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[1e]"></a>LPUART1_IRQHandler</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, isr.o(.text.LPUART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 784<LI>Call Chain = LPUART1_IRQHandler &rArr; my_uart_callback &rArr; fifo_write_buffer &rArr; debug_assert_handler &rArr; debug_protective_handler &rArr; pwm_set_duty &rArr;  debug_assert_handler (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_ClearStatusFlags
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_interrupr_handler
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_uart_callback
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_GetStatusFlags
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[be]"></a>LPUART2_DriverIRQHandler</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, fsl_lpuart.o(.text.LPUART2_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LPUART2_DriverIRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[1f]"></a>LPUART2_IRQHandler</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, isr.o(.text.LPUART2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = LPUART2_IRQHandler &rArr; LPUART_ClearStatusFlags &rArr; LPUART_GetStatusFlags
</UL>
<BR>[Calls]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_ClearStatusFlags
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_GetStatusFlags
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[bf]"></a>LPUART3_DriverIRQHandler</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, fsl_lpuart.o(.text.LPUART3_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LPUART3_DriverIRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[20]"></a>LPUART3_IRQHandler</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, isr.o(.text.LPUART3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = LPUART3_IRQHandler &rArr; LPUART_ClearStatusFlags &rArr; LPUART_GetStatusFlags
</UL>
<BR>[Calls]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_ClearStatusFlags
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_GetStatusFlags
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[c0]"></a>LPUART4_DriverIRQHandler</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, fsl_lpuart.o(.text.LPUART4_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LPUART4_DriverIRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[21]"></a>LPUART4_IRQHandler</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, isr.o(.text.LPUART4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 784<LI>Call Chain = LPUART4_IRQHandler &rArr; my_uart_callback &rArr; fifo_write_buffer &rArr; debug_assert_handler &rArr; debug_protective_handler &rArr; pwm_set_duty &rArr;  debug_assert_handler (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_ClearStatusFlags
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_interrupr_handler
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_uart_callback
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_GetStatusFlags
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[c1]"></a>LPUART5_DriverIRQHandler</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, fsl_lpuart.o(.text.LPUART5_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LPUART5_DriverIRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[22]"></a>LPUART5_IRQHandler</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, isr.o(.text.LPUART5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = LPUART5_IRQHandler &rArr; LPUART_ClearStatusFlags &rArr; LPUART_GetStatusFlags
</UL>
<BR>[Calls]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_ClearStatusFlags
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_GetStatusFlags
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[c2]"></a>LPUART6_DriverIRQHandler</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, fsl_lpuart.o(.text.LPUART6_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LPUART6_DriverIRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[23]"></a>LPUART6_IRQHandler</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, isr.o(.text.LPUART6_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = LPUART6_IRQHandler &rArr; LPUART_ClearStatusFlags &rArr; LPUART_GetStatusFlags
</UL>
<BR>[Calls]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_ClearStatusFlags
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_GetStatusFlags
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[c3]"></a>LPUART7_DriverIRQHandler</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, fsl_lpuart.o(.text.LPUART7_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LPUART7_DriverIRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[c4]"></a>LPUART8_DriverIRQHandler</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, fsl_lpuart.o(.text.LPUART8_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LPUART8_DriverIRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[25]"></a>LPUART8_IRQHandler</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, isr.o(.text.LPUART8_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = LPUART8_IRQHandler &rArr; LPUART_ClearStatusFlags &rArr; LPUART_GetStatusFlags
</UL>
<BR>[Calls]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_ClearStatusFlags
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_GetStatusFlags
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[19b]"></a>LPUART_ClearStatusFlags</STRONG> (Thumb, 128 bytes, Stack size 32 bytes, fsl_lpuart.o(.text.LPUART_ClearStatusFlags))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = LPUART_ClearStatusFlags &rArr; LPUART_GetStatusFlags
</UL>
<BR>[Calls]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_GetStatusFlags
</UL>
<BR>[Called By]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART8_IRQHandler
<LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART6_IRQHandler
<LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART5_IRQHandler
<LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART4_IRQHandler
<LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART3_IRQHandler
<LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART2_IRQHandler
<LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART1_IRQHandler
</UL>

<P><STRONG><a name="[19c]"></a>LPUART_Deinit</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, fsl_lpuart.o(.text.LPUART_Deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 656<LI>Call Chain = LPUART_Deinit &rArr; CLOCK_DisableClock &rArr; CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_GetInstance
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_DisableClock
</UL>
<BR>[Called By]<UL><LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_init
</UL>

<P><STRONG><a name="[20d]"></a>LPUART_DisableInterrupts</STRONG> (Thumb, 130 bytes, Stack size 12 bytes, fsl_lpuart.o(.text.LPUART_DisableInterrupts))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = LPUART_DisableInterrupts
</UL>
<BR>[Called By]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_rx_interrupt
</UL>

<P><STRONG><a name="[20c]"></a>LPUART_EnableInterrupts</STRONG> (Thumb, 118 bytes, Stack size 12 bytes, fsl_lpuart.o(.text.LPUART_EnableInterrupts))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = LPUART_EnableInterrupts
</UL>
<BR>[Called By]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_rx_interrupt
</UL>

<P><STRONG><a name="[19e]"></a>LPUART_GetDefaultConfig</STRONG> (Thumb, 120 bytes, Stack size 16 bytes, fsl_lpuart.o(.text.LPUART_GetDefaultConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = LPUART_GetDefaultConfig &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memset
</UL>
<BR>[Called By]<UL><LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_init
</UL>

<P><STRONG><a name="[19d]"></a>LPUART_GetInstance</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, fsl_lpuart.o(.text.LPUART_GetInstance))
<BR><BR>[Stack]<UL><LI>Max Depth = 600<LI>Call Chain = LPUART_GetInstance &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Called By]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_Deinit
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_Init
</UL>

<P><STRONG><a name="[198]"></a>LPUART_GetStatusFlags</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, fsl_lpuart.o(.text.LPUART_GetStatusFlags))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LPUART_GetStatusFlags
</UL>
<BR>[Called By]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART8_IRQHandler
<LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART6_IRQHandler
<LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART5_IRQHandler
<LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART4_IRQHandler
<LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART3_IRQHandler
<LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART2_IRQHandler
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_ClearStatusFlags
<LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART1_IRQHandler
</UL>

<P><STRONG><a name="[19f]"></a>LPUART_Init</STRONG> (Thumb, 872 bytes, Stack size 56 bytes, fsl_lpuart.o(.text.LPUART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 688<LI>Call Chain = LPUART_Init &rArr; CLOCK_EnableClock &rArr; CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_GetInstance
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_SoftwareReset
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_EnableClock
</UL>
<BR>[Called By]<UL><LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_init
</UL>

<P><STRONG><a name="[3]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, zf_common_vector.o(.text.MemManage_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[1]"></a>NMI_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, zf_common_vector.o(.text.NMI_Handler))
<BR>[Address Reference Count : 2]<UL><LI> startup_mimxrt1064.o(.text)
<LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[1a1]"></a>PIT_Deinit</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, fsl_pit.o(.text.PIT_Deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 648<LI>Call Chain = PIT_Deinit &rArr; CLOCK_DisableClock &rArr; CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_DisableClock
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PIT_GetInstance
</UL>
<BR>[Called By]<UL><LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pit_init
</UL>

<P><STRONG><a name="[84]"></a>PIT_IRQHandler</STRONG> (Thumb, 156 bytes, Stack size 8 bytes, isr.o(.text.PIT_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = PIT_IRQHandler &rArr; send &rArr; uart_rx_interrupt &rArr; interrupt_enable &rArr; EnableIRQ &rArr; __NVIC_EnableIRQ
</UL>
<BR>[Calls]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PIT_ClearStatusFlags
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PIT_GetStatusFlags
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[1a7]"></a>PIT_Init</STRONG> (Thumb, 164 bytes, Stack size 24 bytes, fsl_pit.o(.text.PIT_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 656<LI>Call Chain = PIT_Init &rArr; CLOCK_EnableClock &rArr; CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_EnableClock
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PIT_GetInstance
</UL>
<BR>[Called By]<UL><LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pit_init
</UL>

<P><STRONG><a name="[1a9]"></a>PWM_UpdatePwmDutycycleHighAccuracy</STRONG> (Thumb, 810 bytes, Stack size 32 bytes, fsl_pwm.o(.text.PWM_UpdatePwmDutycycleHighAccuracy))
<BR><BR>[Stack]<UL><LI>Max Depth = 624<LI>Call Chain = PWM_UpdatePwmDutycycleHighAccuracy &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_GetComplementU16
</UL>
<BR>[Called By]<UL><LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pwm_set_duty
</UL>

<P><STRONG><a name="[8]"></a>PendSV_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, zf_common_vector.o(.text.PendSV_Handler))
<BR>[Address Reference Count : 2]<UL><LI> startup_mimxrt1064.o(.text)
<LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[cf]"></a>SAI1_DriverIRQHandler</STRONG> (Thumb, 148 bytes, Stack size 8 bytes, fsl_sai.o(.text.SAI1_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = SAI1_DriverIRQHandler &rArr; SAI_TxGetEnabledInterruptStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SAI_TxGetEnabledInterruptStatus
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SAI_RxGetEnabledInterruptStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[d0]"></a>SAI2_DriverIRQHandler</STRONG> (Thumb, 148 bytes, Stack size 8 bytes, fsl_sai.o(.text.SAI2_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = SAI2_DriverIRQHandler &rArr; SAI_TxGetEnabledInterruptStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SAI_TxGetEnabledInterruptStatus
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SAI_RxGetEnabledInterruptStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[d1]"></a>SAI3_RX_DriverIRQHandler</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, fsl_sai.o(.text.SAI3_RX_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 600<LI>Call Chain = SAI3_RX_DriverIRQHandler &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[d2]"></a>SAI3_TX_DriverIRQHandler</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, fsl_sai.o(.text.SAI3_TX_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 600<LI>Call Chain = SAI3_TX_DriverIRQHandler &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[d3]"></a>SPDIF_DriverIRQHandler</STRONG> (Thumb, 124 bytes, Stack size 8 bytes, fsl_spdif.o(.text.SPDIF_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPDIF_DriverIRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[6]"></a>SVC_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, zf_common_vector.o(.text.SVC_Handler))
<BR>[Address Reference Count : 2]<UL><LI> startup_mimxrt1064.o(.text)
<LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[1b3]"></a>USB_DeviceEhciIsrFunction</STRONG> (Thumb, 140 bytes, Stack size 24 bytes, usb_device_ehci.o(.text.USB_DeviceEhciIsrFunction))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = USB_DeviceEhciIsrFunction &rArr; USB_DeviceEhciInterruptTokenDone &rArr; USB_DeviceEhciCancelControlPipe &rArr; USB_DeviceNotificationTrigger &rArr; USB_DeviceNotification &rArr; USB_DeviceResetNotification &rArr; USB_DeviceControl
</UL>
<BR>[Calls]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeviceEhciInterruptSof
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeviceEhciInterruptPortChange
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeviceEhciInterruptTokenDone
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeviceEhciInterruptReset
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_OTG2_IRQHandler
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_OTG1_IRQHandler
</UL>

<P><STRONG><a name="[1ae]"></a>USB_DeviceNotificationTrigger</STRONG> (Thumb, 74 bytes, Stack size 32 bytes, usb_device_dci.o(.text.USB_DeviceNotificationTrigger))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = USB_DeviceNotificationTrigger &rArr; USB_DeviceNotification &rArr; USB_DeviceResetNotification &rArr; USB_DeviceControl
</UL>
<BR>[Calls]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeviceNotification
</UL>
<BR>[Called By]<UL><LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeviceEhciCancelControlPipe
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeviceEhciInterruptPortChange
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeviceEhciInterruptTokenDone
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeviceEhciInterruptReset
</UL>

<P><STRONG><a name="[7b]"></a>USB_OTG1_IRQHandler</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, zf_driver_usb_cdc.o(.text.USB_OTG1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = USB_OTG1_IRQHandler &rArr; USB_DeviceEhciIsrFunction &rArr; USB_DeviceEhciInterruptTokenDone &rArr; USB_DeviceEhciCancelControlPipe &rArr; USB_DeviceNotificationTrigger &rArr; USB_DeviceNotification &rArr; USB_DeviceResetNotification &rArr; USB_DeviceControl
</UL>
<BR>[Calls]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeviceEhciIsrFunction
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[7a]"></a>USB_OTG2_IRQHandler</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, zf_driver_usb_cdc.o(.text.USB_OTG2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = USB_OTG2_IRQHandler &rArr; USB_DeviceEhciIsrFunction &rArr; USB_DeviceEhciInterruptTokenDone &rArr; USB_DeviceEhciCancelControlPipe &rArr; USB_DeviceNotificationTrigger &rArr; USB_DeviceNotification &rArr; USB_DeviceResetNotification &rArr; USB_DeviceControl
</UL>
<BR>[Calls]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeviceEhciIsrFunction
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[d8]"></a>USDHC1_DriverIRQHandler</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, fsl_usdhc.o(.text.USDHC1_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USDHC1_DriverIRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[d9]"></a>USDHC2_DriverIRQHandler</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, fsl_usdhc.o(.text.USDHC2_DriverIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USDHC2_DriverIRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.text)
</UL>
<P><STRONG><a name="[5]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, zf_common_vector.o(.text.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_mimxrt1064.o(.isr_vector)
</UL>
<P><STRONG><a name="[10f]"></a>__aeabi_assert</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, fsl_assert.o(.text.__aeabi_assert))
<BR><BR>[Stack]<UL><LI>Max Depth = 592<LI>Call Chain = __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DbgConsole_Printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_UpdatePwmDutycycleHighAccuracy
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_Init
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_GetDefaultConfig
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PIT_Init
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PIT_SetTimerPeriod
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PIT_GetDefaultConfig
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinWrite
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPT_Init
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPT_GetDefaultConfig
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPT_SetOutputCompareValue
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_GetDefaultConfig
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_iomuxc
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterInit
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterGetDefaultConfig
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterTransferBlocking
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_SetDiv
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_SetMux
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetPllFreq
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_InitExternalClk
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_GetChannelConversionValue
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_GetChannelStatusFlags
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_ControlGate
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_GetInstance
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ENET_GetInstance
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_HandleIRQ
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EDMA_GetChannelStatusFlags
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_ControlGate
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_GetInstance
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN3_DriverIRQHandler
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN2_DriverIRQHandler
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_DriverIRQHandler
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_ControlGate
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PIT_GetInstance
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_GetInstance
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_ControlGate
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI4_DriverIRQHandler
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI3_DriverIRQHandler
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI2_DriverIRQHandler
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI1_DriverIRQHandler
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_CheckTransferArgument
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterSetDelayScaler
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterSetBaudRate
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_GetInstance
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_CombineWriteData
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_SeparateReadData
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_ControlGate
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_ControlGate
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPT_SetClockDivider
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPT_GetInstance
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SAI3_RX_DriverIRQHandler
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SAI3_TX_DriverIRQHandler
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_ControlGate
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_SetDiv
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_SetMux
</UL>

<P><STRONG><a name="[1b8]"></a>adc_init</STRONG> (Thumb, 114 bytes, Stack size 40 bytes, zf_driver_adc.o(.text.adc_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 744<LI>Call Chain = adc_init &rArr; debug_assert_handler &rArr; debug_protective_handler &rArr; pwm_set_duty &rArr;  debug_assert_handler (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DoAutoCalibration
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_GetDefaultConfig
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_iomuxc
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_assert_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;global_init
</UL>

<P><STRONG><a name="[1b9]"></a>adc_iomuxc</STRONG> (Thumb, 288 bytes, Stack size 16 bytes, zf_driver_adc.o(.text.adc_iomuxc))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = adc_iomuxc &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_iomuxc
</UL>
<BR>[Called By]<UL><LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_init
</UL>

<P><STRONG><a name="[1bc]"></a>afio_init</STRONG> (Thumb, 84 bytes, Stack size 32 bytes, zf_driver_gpio.o(.text.afio_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = afio_init &rArr; IOMUXC_SetPinConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IOMUXC_SetPinConfig
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IOMUXC_SetPinMux
</UL>
<BR>[Called By]<UL><LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_iomuxc
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_iomuxc
</UL>

<P><STRONG><a name="[1bf]"></a>clock_init</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, zf_common_clock.o(.text.clock_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 704<LI>Call Chain = clock_init &rArr; BOARD_InitBootClocks &rArr; BOARD_BootClockRUN &rArr; CLOCK_DisableClock &rArr; CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;interrupt_init
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_delay_init
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BOARD_InitBootClocks
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BOARD_ConfigMPU
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1ba]"></a>debug_assert_handler</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, zf_common_debug.o(.text.debug_assert_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 704<LI>Call Chain = debug_assert_handler &rArr; debug_protective_handler &rArr; pwm_set_duty &rArr;  debug_assert_handler (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;interrupt_global_disable
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_delay
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_output
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_protective_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_iomuxc
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;func_double_to_str
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;func_int_to_str
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pwm_set_duty
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_init
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_show_string
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_init
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fifo_clear
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fifo_write_buffer
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fifo_used
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_init
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fifo_init
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_show_float
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_show_int
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_show_char
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_set_region
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_write_16bit_array
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_write
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_iomuxc
</UL>

<P><STRONG><a name="[1c6]"></a>debug_init</STRONG> (Thumb, 80 bytes, Stack size 40 bytes, zf_common_debug.o(.text.debug_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 832<LI>Call Chain = debug_init &rArr; uart_init &rArr; uart_iomuxc &rArr; debug_assert_handler &rArr; debug_protective_handler &rArr; pwm_set_duty &rArr;  debug_assert_handler (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_output_init
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_output_struct_init
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_rx_interrupt
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_init
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fifo_init
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[19a]"></a>debug_interrupr_handler</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, zf_common_debug.o(.text.debug_interrupr_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 760<LI>Call Chain = debug_interrupr_handler &rArr; fifo_write_buffer &rArr; debug_assert_handler &rArr; debug_protective_handler &rArr; pwm_set_duty &rArr;  debug_assert_handler (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fifo_write_buffer
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_query_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART4_IRQHandler
<LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART1_IRQHandler
</UL>

<P><STRONG><a name="[1c8]"></a>debug_output_init</STRONG> (Thumb, 76 bytes, Stack size 4 bytes, zf_common_debug.o(.text.debug_output_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = debug_output_init
</UL>
<BR>[Called By]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_init
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_debug_init
</UL>

<P><STRONG><a name="[1c7]"></a>debug_output_struct_init</STRONG> (Thumb, 48 bytes, Stack size 4 bytes, zf_common_debug.o(.text.debug_output_struct_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = debug_output_struct_init
</UL>
<BR>[Called By]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_init
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_debug_init
</UL>

<P><STRONG><a name="[1d1]"></a>display_value_float</STRONG> (Thumb, 88 bytes, Stack size 32 bytes, zf_device_ips200.o(.text.display_value_float))
<BR><BR>[Stack]<UL><LI>Max Depth = 1440<LI>Call Chain = display_value_float &rArr; ips200_show_float &rArr; ips200_show_string &rArr; ips200_show_char &rArr; ips200_set_region &rArr; ips200_write_command &rArr; spi_write_8bit &rArr; spi_write_8bit_array &rArr; spi_write &rArr; LPSPI_MasterTransferBlocking &rArr; LPSPI_CheckTransferArgument &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_show_string
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_show_float
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1d3]"></a>display_value_int</STRONG> (Thumb, 74 bytes, Stack size 32 bytes, zf_device_ips200.o(.text.display_value_int))
<BR><BR>[Stack]<UL><LI>Max Depth = 1408<LI>Call Chain = display_value_int &rArr; ips200_show_int &rArr; ips200_show_string &rArr; ips200_show_char &rArr; ips200_set_region &rArr; ips200_write_command &rArr; spi_write_8bit &rArr; spi_write_8bit_array &rArr; spi_write &rArr; LPSPI_MasterTransferBlocking &rArr; LPSPI_CheckTransferArgument &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_show_string
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_show_int
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1d5]"></a>fast_gpio_init</STRONG> (Thumb, 330 bytes, Stack size 32 bytes, zf_driver_gpio.o(.text.fast_gpio_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 640<LI>Call Chain = fast_gpio_init &rArr; GPIO_PinWrite &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinWrite
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetPinInterruptConfig
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_iomuxc
</UL>
<BR>[Called By]<UL><LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_init
</UL>

<P><STRONG><a name="[1d6]"></a>fast_gpio_set_level</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, zf_driver_gpio.o(.text.fast_gpio_set_level))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = fast_gpio_set_level &rArr; GPIO_ClearPinsOutput &rArr; GPIO_PortClear
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ClearPinsOutput
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetPinsOutput
</UL>
<BR>[Called By]<UL><LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_init
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_show_char
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_clear
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_write_8bit_data
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_write_16bit_data
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_write_command
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_write_16bit_data_array
</UL>

<P><STRONG><a name="[1d7]"></a>fifo_clear</STRONG> (Thumb, 172 bytes, Stack size 24 bytes, zf_common_fifo.o(.text.fifo_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 728<LI>Call Chain = fifo_clear &rArr; debug_assert_handler &rArr; debug_protective_handler &rArr; pwm_set_duty &rArr;  debug_assert_handler (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_assert_handler
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memset
</UL>
<BR>[Called By]<UL><LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_uart_callback
</UL>

<P><STRONG><a name="[1ca]"></a>fifo_init</STRONG> (Thumb, 106 bytes, Stack size 32 bytes, zf_common_fifo.o(.text.fifo_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 736<LI>Call Chain = fifo_init &rArr; debug_assert_handler &rArr; debug_protective_handler &rArr; pwm_set_duty &rArr;  debug_assert_handler (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_assert_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_uart_init
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_init
</UL>

<P><STRONG><a name="[1d8]"></a>fifo_used</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, zf_common_fifo.o(.text.fifo_used))
<BR><BR>[Stack]<UL><LI>Max Depth = 720<LI>Call Chain = fifo_used &rArr; debug_assert_handler &rArr; debug_protective_handler &rArr; pwm_set_duty &rArr;  debug_assert_handler (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_assert_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_uart_callback
</UL>

<P><STRONG><a name="[1cd]"></a>fifo_write_buffer</STRONG> (Thumb, 554 bytes, Stack size 40 bytes, zf_common_fifo.o(.text.fifo_write_buffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 744<LI>Call Chain = fifo_write_buffer &rArr; debug_assert_handler &rArr; debug_protective_handler &rArr; pwm_set_duty &rArr;  debug_assert_handler (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fifo_head_offset
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_assert_handler
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_interrupr_handler
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_uart_callback
</UL>

<P><STRONG><a name="[e8]"></a>fputc</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, zf_common_debug.o(.text.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = fputc &rArr; uart_write_byte &rArr; LPUART_WriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_write_byte
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0printf)
</UL>
<P><STRONG><a name="[1db]"></a>func_double_to_str</STRONG> (Thumb, 620 bytes, Stack size 136 bytes, zf_common_function.o(.text.func_double_to_str))
<BR><BR>[Stack]<UL><LI>Max Depth = 840<LI>Call Chain = func_double_to_str &rArr; debug_assert_handler &rArr; debug_protective_handler &rArr; pwm_set_duty &rArr;  debug_assert_handler (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_assert_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_show_float
</UL>

<P><STRONG><a name="[1dc]"></a>func_int_to_str</STRONG> (Thumb, 242 bytes, Stack size 48 bytes, zf_common_function.o(.text.func_int_to_str))
<BR><BR>[Stack]<UL><LI>Max Depth = 752<LI>Call Chain = func_int_to_str &rArr; debug_assert_handler &rArr; debug_protective_handler &rArr; pwm_set_duty &rArr;  debug_assert_handler (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_assert_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_show_int
</UL>

<P><STRONG><a name="[1dd]"></a>global_init</STRONG> (Thumb, 130 bytes, Stack size 24 bytes, system_init.o(.text.global_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 1344<LI>Call Chain = global_init &rArr; ips200_show_string &rArr; ips200_show_char &rArr; ips200_set_region &rArr; ips200_write_command &rArr; spi_write_8bit &rArr; spi_write_8bit_array &rArr; spi_write &rArr; LPSPI_MasterTransferBlocking &rArr; LPSPI_CheckTransferArgument &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_init
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pit_init
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetFreq
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_show_string
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_init
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_set_dir
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_init
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_init
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1de]"></a>gpio_init</STRONG> (Thumb, 130 bytes, Stack size 24 bytes, zf_driver_gpio.o(.text.gpio_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 680<LI>Call Chain = gpio_init &rArr; GPIO_PinInit &rArr; CLOCK_EnableClock &rArr; CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinInit
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_iomuxc
</UL>
<BR>[Called By]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_init
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;global_init
</UL>

<P><STRONG><a name="[1bb]"></a>gpio_iomuxc</STRONG> (Thumb, 244 bytes, Stack size 8 bytes, zf_driver_gpio.o(.text.gpio_iomuxc))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = gpio_iomuxc
</UL>
<BR>[Called By]<UL><LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fast_gpio_init
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_iomuxc
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_init
</UL>

<P><STRONG><a name="[1e3]"></a>interrupt_enable</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, zf_common_interrupt.o(.text.interrupt_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = interrupt_enable &rArr; EnableIRQ &rArr; __NVIC_EnableIRQ
</UL>
<BR>[Calls]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_rx_interrupt
</UL>

<P><STRONG><a name="[1c2]"></a>interrupt_global_disable</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, zf_common_interrupt.o(.text.interrupt_global_disable))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = interrupt_global_disable &rArr; DisableGlobalIRQ
</UL>
<BR>[Calls]<UL><LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DisableGlobalIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_assert_handler
</UL>

<P><STRONG><a name="[1e5]"></a>interrupt_global_enable</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, zf_common_interrupt.o(.text.interrupt_global_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = interrupt_global_enable &rArr; EnableGlobalIRQ
</UL>
<BR>[Calls]<UL><LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EnableGlobalIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_uart_init
</UL>

<P><STRONG><a name="[1c1]"></a>interrupt_init</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, zf_common_interrupt.o(.text.interrupt_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = interrupt_init &rArr; __NVIC_SetPriorityGrouping
</UL>
<BR>[Calls]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clock_init
</UL>

<P><STRONG><a name="[1e8]"></a>interrupt_set_priority</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, zf_common_interrupt.o(.text.interrupt_set_priority))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = interrupt_set_priority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_uart_init
</UL>

<P><STRONG><a name="[e7]"></a>ips200_clear</STRONG> (Thumb, 300 bytes, Stack size 16 bytes, zf_device_ips200.o(.text.ips200_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 920<LI>Call Chain = ips200_clear &rArr; ips200_set_region &rArr; ips200_write_command &rArr; spi_write_8bit &rArr; spi_write_8bit_array &rArr; spi_write &rArr; LPSPI_MasterTransferBlocking &rArr; LPSPI_CheckTransferArgument &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fast_gpio_set_level
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_write_16bit_data_array
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_set_region
</UL>
<BR>[Called By]<UL><LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> zf_device_ips200.o(.text.ips200_debug_init)
</UL>
<P><STRONG><a name="[1e0]"></a>ips200_init</STRONG> (Thumb, 1010 bytes, Stack size 144 bytes, zf_device_ips200.o(.text.ips200_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 1064<LI>Call Chain = ips200_init &rArr; ips200_clear &rArr; ips200_set_region &rArr; ips200_write_command &rArr; spi_write_8bit &rArr; spi_write_8bit_array &rArr; spi_write &rArr; LPSPI_MasterTransferBlocking &rArr; LPSPI_CheckTransferArgument &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fast_gpio_init
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fast_gpio_set_level
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_set_dir
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_delay_ms
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_set_color
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_clear
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_debug_init
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_write_8bit_data
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_write_command
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
</UL>
<BR>[Called By]<UL><LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;global_init
</UL>

<P><STRONG><a name="[1ee]"></a>ips200_set_color</STRONG> (Thumb, 42 bytes, Stack size 4 bytes, zf_device_ips200.o(.text.ips200_set_color))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = ips200_set_color
</UL>
<BR>[Called By]<UL><LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_init
</UL>

<P><STRONG><a name="[1df]"></a>ips200_set_dir</STRONG> (Thumb, 102 bytes, Stack size 8 bytes, zf_device_ips200.o(.text.ips200_set_dir))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ips200_set_dir
</UL>
<BR>[Called By]<UL><LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_init
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;global_init
</UL>

<P><STRONG><a name="[1f3]"></a>ips200_show_char</STRONG> (Thumb, 794 bytes, Stack size 384 bytes, zf_device_ips200.o(.text.ips200_show_char))
<BR><BR>[Stack]<UL><LI>Max Depth = 1288<LI>Call Chain = ips200_show_char &rArr; ips200_set_region &rArr; ips200_write_command &rArr; spi_write_8bit &rArr; spi_write_8bit_array &rArr; spi_write &rArr; LPSPI_MasterTransferBlocking &rArr; LPSPI_CheckTransferArgument &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fast_gpio_set_level
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_assert_handler
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_write_16bit_data_array
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_set_region
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_show_string
</UL>

<P><STRONG><a name="[1d2]"></a>ips200_show_float</STRONG> (Thumb, 376 bytes, Stack size 88 bytes, zf_device_ips200.o(.text.ips200_show_float))
<BR><BR>[Stack]<UL><LI>Max Depth = 1408<LI>Call Chain = ips200_show_float &rArr; ips200_show_string &rArr; ips200_show_char &rArr; ips200_set_region &rArr; ips200_write_command &rArr; spi_write_8bit &rArr; spi_write_8bit_array &rArr; spi_write &rArr; LPSPI_MasterTransferBlocking &rArr; LPSPI_CheckTransferArgument &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;func_double_to_str
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_assert_handler
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_show_string
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memset
</UL>
<BR>[Called By]<UL><LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_value_float
</UL>

<P><STRONG><a name="[1d4]"></a>ips200_show_int</STRONG> (Thumb, 260 bytes, Stack size 56 bytes, zf_device_ips200.o(.text.ips200_show_int))
<BR><BR>[Stack]<UL><LI>Max Depth = 1376<LI>Call Chain = ips200_show_int &rArr; ips200_show_string &rArr; ips200_show_char &rArr; ips200_set_region &rArr; ips200_write_command &rArr; spi_write_8bit &rArr; spi_write_8bit_array &rArr; spi_write &rArr; LPSPI_MasterTransferBlocking &rArr; LPSPI_CheckTransferArgument &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;func_int_to_str
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_assert_handler
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_show_string
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memset
</UL>
<BR>[Called By]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_value_int
</UL>

<P><STRONG><a name="[e6]"></a>ips200_show_string</STRONG> (Thumb, 220 bytes, Stack size 32 bytes, zf_device_ips200.o(.text.ips200_show_string))
<BR><BR>[Stack]<UL><LI>Max Depth = 1320<LI>Call Chain = ips200_show_string &rArr; ips200_show_char &rArr; ips200_set_region &rArr; ips200_write_command &rArr; spi_write_8bit &rArr; spi_write_8bit_array &rArr; spi_write &rArr; LPSPI_MasterTransferBlocking &rArr; LPSPI_CheckTransferArgument &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_assert_handler
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_show_char
</UL>
<BR>[Called By]<UL><LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_value_float
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_value_int
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;global_init
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_show_float
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_show_int
</UL>
<BR>[Address Reference Count : 1]<UL><LI> zf_device_ips200.o(.text.ips200_debug_init)
</UL>
<P><STRONG><a name="[1e2]"></a>key_init</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, zf_device_key.o(.text.key_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 728<LI>Call Chain = key_init &rArr; debug_assert_handler &rArr; debug_protective_handler &rArr; pwm_set_duty &rArr;  debug_assert_handler (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_assert_handler
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_init
</UL>
<BR>[Called By]<UL><LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;global_init
</UL>

<P><STRONG><a name="[a9]"></a>main</STRONG> (Thumb, 296 bytes, Stack size 40 bytes, main.o(.text.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 1480<LI>Call Chain = main &rArr; display_value_float &rArr; ips200_show_float &rArr; ips200_show_string &rArr; ips200_show_char &rArr; ips200_set_region &rArr; ips200_write_command &rArr; spi_write_8bit &rArr; spi_write_8bit_array &rArr; spi_write &rArr; LPSPI_MasterTransferBlocking &rArr; LPSPI_CheckTransferArgument &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_value_float
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_value_int
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_uart_init
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;global_init
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_delay_ms
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_init
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clock_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[199]"></a>my_uart_callback</STRONG> (Thumb, 1064 bytes, Stack size 32 bytes, serial.o(.text.my_uart_callback))
<BR><BR>[Stack]<UL><LI>Max Depth = 776<LI>Call Chain = my_uart_callback &rArr; fifo_write_buffer &rArr; debug_assert_handler &rArr; debug_protective_handler &rArr; pwm_set_duty &rArr;  debug_assert_handler (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fifo_clear
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fifo_write_buffer
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fifo_used
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_query_byte
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sscanf
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memset
</UL>
<BR>[Called By]<UL><LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART4_IRQHandler
<LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART1_IRQHandler
</UL>

<P><STRONG><a name="[1f8]"></a>my_uart_init</STRONG> (Thumb, 130 bytes, Stack size 32 bytes, serial.o(.text.my_uart_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 824<LI>Call Chain = my_uart_init &rArr; uart_init &rArr; uart_iomuxc &rArr; debug_assert_handler &rArr; debug_protective_handler &rArr; pwm_set_duty &rArr;  debug_assert_handler (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;interrupt_global_enable
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;interrupt_set_priority
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_rx_interrupt
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_init
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fifo_init
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1e1]"></a>pit_init</STRONG> (Thumb, 142 bytes, Stack size 32 bytes, zf_driver_pit.o(.text.pit_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 688<LI>Call Chain = pit_init &rArr; PIT_Init &rArr; CLOCK_EnableClock &rArr; CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PIT_Deinit
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PIT_Init
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EnableIRQ
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PIT_SetTimerChainMode
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PIT_EnableInterrupts
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PIT_SetTimerPeriod
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PIT_GetDefaultConfig
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PIT_StartTimer
</UL>
<BR>[Called By]<UL><LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;global_init
</UL>

<P><STRONG><a name="[1cf]"></a>pwm_set_duty</STRONG> (Thumb, 260 bytes, Stack size 40 bytes, zf_driver_pwm.o(.text.pwm_set_duty))
<BR><BR>[Stack]<UL><LI>Max Depth = 664 + In Cycle
<LI>Call Chain = pwm_set_duty &rArr;  debug_assert_handler (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_UpdatePwmDutycycleHighAccuracy
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_SetPwmLdok
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_assert_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_protective_handler
</UL>

<P><STRONG><a name="[1a5]"></a>send</STRONG> (Thumb, 236 bytes, Stack size 24 bytes, serial.o(.text.send))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = send &rArr; uart_rx_interrupt &rArr; interrupt_enable &rArr; EnableIRQ &rArr; __NVIC_EnableIRQ
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_rx_interrupt
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;printf
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PIT_IRQHandler
</UL>

<P><STRONG><a name="[1ed]"></a>spi_init</STRONG> (Thumb, 684 bytes, Stack size 96 bytes, zf_driver_spi.o(.text.spi_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 856<LI>Call Chain = spi_init &rArr; LPSPI_MasterInit &rArr; LPSPI_MasterSetDelayTimes &rArr; LPSPI_MasterSetDelayScaler &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_assert_handler
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetFreq
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_Reset
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterInit
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterGetDefaultConfig
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_iomuxc
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_DisableInterrupts
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_ClearStatusFlags
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_FlushFifo
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_Enable
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_SetDiv
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_SetMux
</UL>
<BR>[Called By]<UL><LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_init
</UL>

<P><STRONG><a name="[1fe]"></a>spi_iomuxc</STRONG> (Thumb, 1508 bytes, Stack size 32 bytes, zf_driver_spi.o(.text.spi_iomuxc))
<BR><BR>[Stack]<UL><LI>Max Depth = 736<LI>Call Chain = spi_iomuxc &rArr; debug_assert_handler &rArr; debug_protective_handler &rArr; pwm_set_duty &rArr;  debug_assert_handler (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;afio_init
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_assert_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
</UL>

<P><STRONG><a name="[204]"></a>spi_write</STRONG> (Thumb, 282 bytes, Stack size 48 bytes, zf_driver_spi.o(.text.spi_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 776<LI>Call Chain = spi_write &rArr; LPSPI_MasterTransferBlocking &rArr; LPSPI_CheckTransferArgument &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_assert_handler
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterTransferBlocking
</UL>
<BR>[Called By]<UL><LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_write_16bit_array
<LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_write_16bit
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_write_8bit_array
</UL>

<P><STRONG><a name="[1f4]"></a>spi_write_16bit</STRONG> (Thumb, 38 bytes, Stack size 24 bytes, zf_driver_spi.o(.text.spi_write_16bit))
<BR><BR>[Stack]<UL><LI>Max Depth = 800<LI>Call Chain = spi_write_16bit &rArr; spi_write &rArr; LPSPI_MasterTransferBlocking &rArr; LPSPI_CheckTransferArgument &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_write
</UL>
<BR>[Called By]<UL><LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_write_16bit_data
</UL>

<P><STRONG><a name="[1f6]"></a>spi_write_16bit_array</STRONG> (Thumb, 66 bytes, Stack size 32 bytes, zf_driver_spi.o(.text.spi_write_16bit_array))
<BR><BR>[Stack]<UL><LI>Max Depth = 808<LI>Call Chain = spi_write_16bit_array &rArr; spi_write &rArr; LPSPI_MasterTransferBlocking &rArr; LPSPI_CheckTransferArgument &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_assert_handler
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_write
</UL>
<BR>[Called By]<UL><LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_write_16bit_data_array
</UL>

<P><STRONG><a name="[1f7]"></a>spi_write_8bit</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, zf_driver_spi.o(.text.spi_write_8bit))
<BR><BR>[Stack]<UL><LI>Max Depth = 840<LI>Call Chain = spi_write_8bit &rArr; spi_write_8bit_array &rArr; spi_write &rArr; LPSPI_MasterTransferBlocking &rArr; LPSPI_CheckTransferArgument &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_write_8bit_array
</UL>
<BR>[Called By]<UL><LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_write_8bit_data
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_write_command
</UL>

<P><STRONG><a name="[205]"></a>spi_write_8bit_array</STRONG> (Thumb, 44 bytes, Stack size 48 bytes, zf_driver_spi.o(.text.spi_write_8bit_array))
<BR><BR>[Stack]<UL><LI>Max Depth = 824<LI>Call Chain = spi_write_8bit_array &rArr; spi_write &rArr; LPSPI_MasterTransferBlocking &rArr; LPSPI_CheckTransferArgument &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_write
</UL>
<BR>[Called By]<UL><LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_write_8bit
</UL>

<P><STRONG><a name="[1c0]"></a>system_delay_init</STRONG> (Thumb, 54 bytes, Stack size 32 bytes, zf_driver_delay.o(.text.system_delay_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 680<LI>Call Chain = system_delay_init &rArr; GPT_Deinit &rArr; CLOCK_DisableClock &rArr; CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPT_Deinit
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPT_Init
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPT_GetDefaultConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clock_init
</UL>

<P><STRONG><a name="[1ef]"></a>system_delay_ms</STRONG> (Thumb, 88 bytes, Stack size 24 bytes, zf_driver_delay.o(.text.system_delay_ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 632<LI>Call Chain = system_delay_ms &rArr; GPT_SetOutputCompareValue &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPT_StopTimer
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPT_ClearStatusFlags
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPT_GetStatusFlags
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPT_StartTimer
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPT_SetOutputCompareValue
</UL>
<BR>[Called By]<UL><LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_init
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ea]"></a>type_default_callback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, zf_device_type.o(.text.type_default_callback))
<BR>[Address Reference Count : 5]<UL><LI> zf_device_type.o(.data.wireless_module_uart_handler)
<LI> zf_device_type.o(.data.wireless_module_spi_handler)
<LI> zf_device_type.o(.data.tof_module_exti_handler)
<LI> zf_device_type.o(.data.camera_uart_handler)
<LI> zf_device_type.o(.data.flexio_camera_vsync_handler)
</UL>
<P><STRONG><a name="[1c9]"></a>uart_init</STRONG> (Thumb, 158 bytes, Stack size 56 bytes, zf_driver_uart.o(.text.uart_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 792<LI>Call Chain = uart_init &rArr; uart_iomuxc &rArr; debug_assert_handler &rArr; debug_protective_handler &rArr; pwm_set_duty &rArr;  debug_assert_handler (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_Deinit
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_Init
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_GetDefaultConfig
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BOARD_DebugConsoleSrcFreq
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_iomuxc
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_assert_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_uart_init
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_init
</UL>

<P><STRONG><a name="[20a]"></a>uart_iomuxc</STRONG> (Thumb, 1234 bytes, Stack size 32 bytes, zf_driver_uart.o(.text.uart_iomuxc))
<BR><BR>[Stack]<UL><LI>Max Depth = 736<LI>Call Chain = uart_iomuxc &rArr; debug_assert_handler &rArr; debug_protective_handler &rArr; pwm_set_duty &rArr;  debug_assert_handler (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;afio_init
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_assert_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_init
</UL>

<P><STRONG><a name="[1cc]"></a>uart_query_byte</STRONG> (Thumb, 86 bytes, Stack size 24 bytes, zf_driver_uart.o(.text.uart_query_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = uart_query_byte &rArr; LPUART_ReadByte
</UL>
<BR>[Calls]<UL><LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_ReadByte
</UL>
<BR>[Called By]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_interrupr_handler
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_uart_callback
</UL>

<P><STRONG><a name="[1cb]"></a>uart_rx_interrupt</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, zf_driver_uart.o(.text.uart_rx_interrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = uart_rx_interrupt &rArr; interrupt_enable &rArr; EnableIRQ &rArr; __NVIC_EnableIRQ
</UL>
<BR>[Calls]<UL><LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_DisableInterrupts
<LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_EnableInterrupts
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;interrupt_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_uart_init
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_init
</UL>

<P><STRONG><a name="[1da]"></a>uart_write_byte</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, zf_driver_uart.o(.text.uart_write_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = uart_write_byte &rArr; LPUART_WriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_WriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_write_string
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[1d0]"></a>uart_write_string</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, zf_driver_uart.o(.text.uart_write_string))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = uart_write_string &rArr; uart_write_byte &rArr; LPUART_WriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_uart_str_output
</UL>

<P><STRONG><a name="[20f]"></a>__0printf</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[23e]"></a>__1printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[23f]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[240]"></a>__c89printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[1fd]"></a>printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
</UL>

<P><STRONG><a name="[211]"></a>__0sprintf</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[241]"></a>__1sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[242]"></a>__2sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[243]"></a>__c89sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[1ce]"></a>sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_output
</UL>

<P><STRONG><a name="[21c]"></a>__ARM_fpclassify</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, fpclassify.o(i.__ARM_fpclassify))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __ARM_fpclassify
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[156]"></a>__hardfp_modf</STRONG> (Thumb, 186 bytes, Stack size 24 bytes, modf.o(i.__hardfp_modf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __hardfp_modf
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DbgConsole_ConvertFloatRadixNumToString
</UL>

<P><STRONG><a name="[157]"></a>__hardfp_pow</STRONG> (Thumb, 2108 bytes, Stack size 192 bytes, pow.o(i.__hardfp_pow))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_overflow
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_divzero
<LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DbgConsole_ConvertFloatRadixNumToString
</UL>

<P><STRONG><a name="[21a]"></a>__kernel_poly</STRONG> (Thumb, 112 bytes, Stack size 0 bytes, poly.o(i.__kernel_poly))
<BR><BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[214]"></a>__mathlib_dbl_divzero</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, dunder.o(i.__mathlib_dbl_divzero))
<BR><BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[212]"></a>__mathlib_dbl_infnan2</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dunder.o(i.__mathlib_dbl_infnan2))
<BR><BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[218]"></a>__mathlib_dbl_invalid</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, dunder.o(i.__mathlib_dbl_invalid))
<BR><BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[215]"></a>__mathlib_dbl_overflow</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, dunder.o(i.__mathlib_dbl_overflow))
<BR><BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[219]"></a>__mathlib_dbl_underflow</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, dunder.o(i.__mathlib_dbl_underflow))
<BR><BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[213]"></a>__set_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__set_errno))
<BR><BR>[Called By]<UL><LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[fc]"></a>_is_digit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, scanf_fp.o(i._is_digit), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[217]"></a>fabs</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, fabs.o(i.fabs))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[216]"></a>sqrt</STRONG> (Thumb, 106 bytes, Stack size 24 bytes, sqrt.o(i.sqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[1a4]"></a>PIT_GetStatusFlags</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, isr.o(.text.PIT_GetStatusFlags))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = PIT_GetStatusFlags
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PIT_IRQHandler
</UL>

<P><STRONG><a name="[1a6]"></a>PIT_ClearStatusFlags</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, isr.o(.text.PIT_ClearStatusFlags))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = PIT_ClearStatusFlags
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PIT_IRQHandler
</UL>

<P><STRONG><a name="[167]"></a>GPIO_GetPinsInterruptFlags</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, isr.o(.text.GPIO_GetPinsInterruptFlags))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = GPIO_GetPinsInterruptFlags &rArr; GPIO_PortGetInterruptFlags
</UL>
<BR>[Calls]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PortGetInterruptFlags
</UL>
<BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO3_Combined_0_15_IRQHandler
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO2_Combined_16_31_IRQHandler
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO2_Combined_0_15_IRQHandler
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO1_Combined_16_31_IRQHandler
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO1_Combined_0_15_IRQHandler
</UL>

<P><STRONG><a name="[168]"></a>GPIO_ClearPinsInterruptFlags</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, isr.o(.text.GPIO_ClearPinsInterruptFlags))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = GPIO_ClearPinsInterruptFlags &rArr; GPIO_PortClearInterruptFlags
</UL>
<BR>[Calls]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PortClearInterruptFlags
</UL>
<BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO3_Combined_0_15_IRQHandler
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO2_Combined_16_31_IRQHandler
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO2_Combined_0_15_IRQHandler
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO1_Combined_16_31_IRQHandler
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO1_Combined_0_15_IRQHandler
</UL>

<P><STRONG><a name="[16d]"></a>GPIO_PortGetInterruptFlags</STRONG> (Thumb, 12 bytes, Stack size 4 bytes, isr.o(.text.GPIO_PortGetInterruptFlags))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = GPIO_PortGetInterruptFlags
</UL>
<BR>[Called By]<UL><LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_GetPinsInterruptFlags
</UL>

<P><STRONG><a name="[169]"></a>GPIO_PortClearInterruptFlags</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, isr.o(.text.GPIO_PortClearInterruptFlags))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = GPIO_PortClearInterruptFlags
</UL>
<BR>[Called By]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ClearPinsInterruptFlags
</UL>

<P><STRONG><a name="[1c3]"></a>debug_protective_handler</STRONG> (Thumb, 170 bytes, Stack size 16 bytes, zf_common_debug.o(.text.debug_protective_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 680<LI>Call Chain = debug_protective_handler &rArr; pwm_set_duty &rArr;  debug_assert_handler (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pwm_set_duty
</UL>
<BR>[Called By]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_assert_handler
</UL>

<P><STRONG><a name="[1c4]"></a>debug_output</STRONG> (Thumb, 944 bytes, Stack size 640 bytes, zf_common_debug.o(.text.debug_output))
<BR><BR>[Stack]<UL><LI>Max Depth = 664<LI>Call Chain = debug_output &rArr; sprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sprintf
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memset
</UL>
<BR>[Called By]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_assert_handler
</UL>

<P><STRONG><a name="[1c5]"></a>debug_delay</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, zf_common_debug.o(.text.debug_delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = debug_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_assert_handler
</UL>

<P><STRONG><a name="[e5]"></a>debug_uart_str_output</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, zf_common_debug.o(.text.debug_uart_str_output))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = debug_uart_str_output &rArr; uart_write_string &rArr; uart_write_byte &rArr; LPUART_WriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_write_string
</UL>
<BR>[Address Reference Count : 1]<UL><LI> zf_common_debug.o(.text.debug_init)
</UL>
<P><STRONG><a name="[1d9]"></a>fifo_head_offset</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, zf_common_fifo.o(.text.fifo_head_offset))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = fifo_head_offset
</UL>
<BR>[Called By]<UL><LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fifo_write_buffer
</UL>

<P><STRONG><a name="[1e6]"></a>EnableGlobalIRQ</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, zf_common_interrupt.o(.text.EnableGlobalIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EnableGlobalIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;interrupt_global_enable
</UL>

<P><STRONG><a name="[1e4]"></a>DisableGlobalIRQ</STRONG> (Thumb, 26 bytes, Stack size 12 bytes, zf_common_interrupt.o(.text.DisableGlobalIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = DisableGlobalIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;interrupt_global_disable
</UL>

<P><STRONG><a name="[162]"></a>EnableIRQ</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, zf_common_interrupt.o(.text.EnableIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = EnableIRQ &rArr; __NVIC_EnableIRQ
</UL>
<BR>[Calls]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;interrupt_enable
</UL>

<P><STRONG><a name="[163]"></a>__NVIC_EnableIRQ</STRONG> (Thumb, 48 bytes, Stack size 4 bytes, zf_common_interrupt.o(.text.__NVIC_EnableIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EnableIRQ
</UL>

<P><STRONG><a name="[1e9]"></a>__NVIC_SetPriority</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, zf_common_interrupt.o(.text.__NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;interrupt_set_priority
</UL>

<P><STRONG><a name="[1e7]"></a>__NVIC_SetPriorityGrouping</STRONG> (Thumb, 60 bytes, Stack size 12 bytes, zf_common_interrupt.o(.text.__NVIC_SetPriorityGrouping))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = __NVIC_SetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;interrupt_init
</UL>

<P><STRONG><a name="[17c]"></a>GPT_SetOutputCompareValue</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, zf_driver_delay.o(.text.GPT_SetOutputCompareValue))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = GPT_SetOutputCompareValue &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Called By]<UL><LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_delay_ms
</UL>

<P><STRONG><a name="[206]"></a>GPT_StartTimer</STRONG> (Thumb, 18 bytes, Stack size 4 bytes, zf_driver_delay.o(.text.GPT_StartTimer))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = GPT_StartTimer
</UL>
<BR>[Called By]<UL><LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_delay_ms
</UL>

<P><STRONG><a name="[207]"></a>GPT_GetStatusFlags</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, zf_driver_delay.o(.text.GPT_GetStatusFlags))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = GPT_GetStatusFlags
</UL>
<BR>[Called By]<UL><LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_delay_ms
</UL>

<P><STRONG><a name="[208]"></a>GPT_ClearStatusFlags</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, zf_driver_delay.o(.text.GPT_ClearStatusFlags))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = GPT_ClearStatusFlags
</UL>
<BR>[Called By]<UL><LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_delay_ms
</UL>

<P><STRONG><a name="[209]"></a>GPT_StopTimer</STRONG> (Thumb, 18 bytes, Stack size 4 bytes, zf_driver_delay.o(.text.GPT_StopTimer))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = GPT_StopTimer
</UL>
<BR>[Called By]<UL><LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_delay_ms
</UL>

<P><STRONG><a name="[1bd]"></a>IOMUXC_SetPinMux</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, zf_driver_gpio.o(.text.IOMUXC_SetPinMux))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = IOMUXC_SetPinMux
</UL>
<BR>[Called By]<UL><LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;afio_init
</UL>

<P><STRONG><a name="[1be]"></a>IOMUXC_SetPinConfig</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, zf_driver_gpio.o(.text.IOMUXC_SetPinConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = IOMUXC_SetPinConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;afio_init
</UL>

<P><STRONG><a name="[173]"></a>GPIO_SetPinsOutput</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, zf_driver_gpio.o(.text.GPIO_SetPinsOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = GPIO_SetPinsOutput &rArr; GPIO_PortSet
</UL>
<BR>[Calls]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PortSet
</UL>
<BR>[Called By]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fast_gpio_set_level
</UL>

<P><STRONG><a name="[16a]"></a>GPIO_ClearPinsOutput</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, zf_driver_gpio.o(.text.GPIO_ClearPinsOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = GPIO_ClearPinsOutput &rArr; GPIO_PortClear
</UL>
<BR>[Calls]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PortClear
</UL>
<BR>[Called By]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fast_gpio_set_level
</UL>

<P><STRONG><a name="[174]"></a>GPIO_PortSet</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, zf_driver_gpio.o(.text.GPIO_PortSet))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = GPIO_PortSet
</UL>
<BR>[Called By]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetPinsOutput
</UL>

<P><STRONG><a name="[16b]"></a>GPIO_PortClear</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, zf_driver_gpio.o(.text.GPIO_PortClear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = GPIO_PortClear
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ClearPinsOutput
</UL>

<P><STRONG><a name="[171]"></a>GPIO_SetPinInterruptConfig</STRONG> (Thumb, 28 bytes, Stack size 24 bytes, zf_driver_gpio.o(.text.GPIO_SetPinInterruptConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = GPIO_SetPinInterruptConfig &rArr; GPIO_PinSetInterruptConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinSetInterruptConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fast_gpio_init
</UL>

<P><STRONG><a name="[1fb]"></a>PIT_StartTimer</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, zf_driver_pit.o(.text.PIT_StartTimer))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = PIT_StartTimer
</UL>
<BR>[Called By]<UL><LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pit_init
</UL>

<P><STRONG><a name="[1a3]"></a>PIT_GetDefaultConfig</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, zf_driver_pit.o(.text.PIT_GetDefaultConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 600<LI>Call Chain = PIT_GetDefaultConfig &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Called By]<UL><LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pit_init
</UL>

<P><STRONG><a name="[1a8]"></a>PIT_SetTimerPeriod</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, zf_driver_pit.o(.text.PIT_SetTimerPeriod))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = PIT_SetTimerPeriod &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Called By]<UL><LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pit_init
</UL>

<P><STRONG><a name="[1f9]"></a>PIT_EnableInterrupts</STRONG> (Thumb, 36 bytes, Stack size 12 bytes, zf_driver_pit.o(.text.PIT_EnableInterrupts))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = PIT_EnableInterrupts
</UL>
<BR>[Called By]<UL><LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pit_init
</UL>

<P><STRONG><a name="[1fa]"></a>PIT_SetTimerChainMode</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, zf_driver_pit.o(.text.PIT_SetTimerChainMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = PIT_SetTimerChainMode
</UL>
<BR>[Called By]<UL><LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pit_init
</UL>

<P><STRONG><a name="[164]"></a>EnableIRQ</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, zf_driver_pit.o(.text.EnableIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = EnableIRQ &rArr; __NVIC_EnableIRQ
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pit_init
</UL>

<P><STRONG><a name="[165]"></a>__NVIC_EnableIRQ</STRONG> (Thumb, 48 bytes, Stack size 4 bytes, zf_driver_pit.o(.text.__NVIC_EnableIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EnableIRQ
</UL>

<P><STRONG><a name="[20e]"></a>LPUART_WriteByte</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, zf_driver_uart.o(.text.LPUART_WriteByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LPUART_WriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_write_byte
</UL>

<P><STRONG><a name="[20b]"></a>LPUART_ReadByte</STRONG> (Thumb, 128 bytes, Stack size 20 bytes, zf_driver_uart.o(.text.LPUART_ReadByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = LPUART_ReadByte
</UL>
<BR>[Called By]<UL><LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_query_byte
</UL>

<P><STRONG><a name="[1fc]"></a>PWM_SetPwmLdok</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, zf_driver_pwm.o(.text.PWM_SetPwmLdok))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = PWM_SetPwmLdok
</UL>
<BR>[Called By]<UL><LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pwm_set_duty
</UL>

<P><STRONG><a name="[151]"></a>CLOCK_SetMux</STRONG> (Thumb, 138 bytes, Stack size 24 bytes, zf_driver_spi.o(.text.CLOCK_SetMux))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = CLOCK_SetMux &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Called By]<UL><LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
</UL>

<P><STRONG><a name="[150]"></a>CLOCK_SetDiv</STRONG> (Thumb, 138 bytes, Stack size 24 bytes, zf_driver_spi.o(.text.CLOCK_SetDiv))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = CLOCK_SetDiv &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Called By]<UL><LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
</UL>

<P><STRONG><a name="[200]"></a>LPSPI_Enable</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, zf_driver_spi.o(.text.LPSPI_Enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LPSPI_Enable
</UL>
<BR>[Called By]<UL><LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
</UL>

<P><STRONG><a name="[201]"></a>LPSPI_FlushFifo</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, zf_driver_spi.o(.text.LPSPI_FlushFifo))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LPSPI_FlushFifo
</UL>
<BR>[Called By]<UL><LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
</UL>

<P><STRONG><a name="[202]"></a>LPSPI_ClearStatusFlags</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, zf_driver_spi.o(.text.LPSPI_ClearStatusFlags))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LPSPI_ClearStatusFlags
</UL>
<BR>[Called By]<UL><LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
</UL>

<P><STRONG><a name="[203]"></a>LPSPI_DisableInterrupts</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, zf_driver_spi.o(.text.LPSPI_DisableInterrupts))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LPSPI_DisableInterrupts
</UL>
<BR>[Called By]<UL><LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
</UL>

<P><STRONG><a name="[1ea]"></a>ips200_set_region</STRONG> (Thumb, 204 bytes, Stack size 32 bytes, zf_device_ips200.o(.text.ips200_set_region))
<BR><BR>[Stack]<UL><LI>Max Depth = 904<LI>Call Chain = ips200_set_region &rArr; ips200_write_command &rArr; spi_write_8bit &rArr; spi_write_8bit_array &rArr; spi_write &rArr; LPSPI_MasterTransferBlocking &rArr; LPSPI_CheckTransferArgument &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_assert_handler
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_write_16bit_data
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_write_command
</UL>
<BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_show_char
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_clear
</UL>

<P><STRONG><a name="[1eb]"></a>ips200_write_16bit_data_array</STRONG> (Thumb, 164 bytes, Stack size 32 bytes, zf_device_ips200.o(.text.ips200_write_16bit_data_array))
<BR><BR>[Stack]<UL><LI>Max Depth = 840<LI>Call Chain = ips200_write_16bit_data_array &rArr; spi_write_16bit_array &rArr; spi_write &rArr; LPSPI_MasterTransferBlocking &rArr; LPSPI_CheckTransferArgument &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fast_gpio_set_level
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_write_data
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_write_16bit_array
</UL>
<BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_show_char
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_clear
</UL>

<P><STRONG><a name="[1f0]"></a>ips200_write_command</STRONG> (Thumb, 132 bytes, Stack size 32 bytes, zf_device_ips200.o(.text.ips200_write_command))
<BR><BR>[Stack]<UL><LI>Max Depth = 872<LI>Call Chain = ips200_write_command &rArr; spi_write_8bit &rArr; spi_write_8bit_array &rArr; spi_write &rArr; LPSPI_MasterTransferBlocking &rArr; LPSPI_CheckTransferArgument &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fast_gpio_set_level
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_write_data
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_write_8bit
</UL>
<BR>[Called By]<UL><LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_init
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_set_region
</UL>

<P><STRONG><a name="[1f2]"></a>ips200_write_16bit_data</STRONG> (Thumb, 138 bytes, Stack size 32 bytes, zf_device_ips200.o(.text.ips200_write_16bit_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 832<LI>Call Chain = ips200_write_16bit_data &rArr; spi_write_16bit &rArr; spi_write &rArr; LPSPI_MasterTransferBlocking &rArr; LPSPI_CheckTransferArgument &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fast_gpio_set_level
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_write_data
<LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_write_16bit
</UL>
<BR>[Called By]<UL><LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_set_region
</UL>

<P><STRONG><a name="[1f5]"></a>ips200_write_data</STRONG> (Thumb, 52 bytes, Stack size 4 bytes, zf_device_ips200.o(.text.ips200_write_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = ips200_write_data
</UL>
<BR>[Called By]<UL><LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_write_8bit_data
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_write_16bit_data
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_write_command
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_write_16bit_data_array
</UL>

<P><STRONG><a name="[1f1]"></a>ips200_write_8bit_data</STRONG> (Thumb, 114 bytes, Stack size 32 bytes, zf_device_ips200.o(.text.ips200_write_8bit_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 872<LI>Call Chain = ips200_write_8bit_data &rArr; spi_write_8bit &rArr; spi_write_8bit_array &rArr; spi_write &rArr; LPSPI_MasterTransferBlocking &rArr; LPSPI_CheckTransferArgument &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fast_gpio_set_level
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_write_data
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_write_8bit
</UL>
<BR>[Called By]<UL><LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_init
</UL>

<P><STRONG><a name="[1ec]"></a>ips200_debug_init</STRONG> (Thumb, 138 bytes, Stack size 32 bytes, zf_device_ips200.o(.text.ips200_debug_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = ips200_debug_init &rArr; debug_output_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_output_init
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_output_struct_init
</UL>
<BR>[Called By]<UL><LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ips200_init
</UL>

<P><STRONG><a name="[111]"></a>ADC_GetInstance</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, fsl_adc.o(.text.ADC_GetInstance))
<BR><BR>[Stack]<UL><LI>Max Depth = 600<LI>Call Chain = ADC_GetInstance &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
</UL>

<P><STRONG><a name="[113]"></a>CLOCK_EnableClock</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, fsl_adc.o(.text.CLOCK_EnableClock))
<BR><BR>[Stack]<UL><LI>Max Depth = 632<LI>Call Chain = CLOCK_EnableClock &rArr; CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_ControlGate
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
</UL>

<P><STRONG><a name="[12c]"></a>CLOCK_ControlGate</STRONG> (Thumb, 102 bytes, Stack size 24 bytes, fsl_adc.o(.text.CLOCK_ControlGate))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_SDK_AtomicLocalClearAndSet4Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_EnableClock
</UL>

<P><STRONG><a name="[10b]"></a>ADC_EnableHardwareTrigger</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, fsl_adc.o(.text.ADC_EnableHardwareTrigger))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADC_EnableHardwareTrigger
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DoAutoCalibration
</UL>

<P><STRONG><a name="[10c]"></a>ADC_GetStatusFlags</STRONG> (Thumb, 12 bytes, Stack size 4 bytes, fsl_adc.o(.text.ADC_GetStatusFlags))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = ADC_GetStatusFlags
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DoAutoCalibration
</UL>

<P><STRONG><a name="[10d]"></a>ADC_GetChannelStatusFlags</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, fsl_adc.o(.text.ADC_GetChannelStatusFlags))
<BR><BR>[Stack]<UL><LI>Max Depth = 600<LI>Call Chain = ADC_GetChannelStatusFlags &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DoAutoCalibration
</UL>

<P><STRONG><a name="[10e]"></a>ADC_GetChannelConversionValue</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, fsl_adc.o(.text.ADC_GetChannelConversionValue))
<BR><BR>[Stack]<UL><LI>Max Depth = 600<LI>Call Chain = ADC_GetChannelConversionValue &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DoAutoCalibration
</UL>

<P><STRONG><a name="[12d]"></a>_SDK_AtomicLocalClearAndSet4Byte</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, fsl_adc.o(.text._SDK_AtomicLocalClearAndSet4Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _SDK_AtomicLocalClearAndSet4Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_ControlGate
</UL>

<P><STRONG><a name="[143]"></a>CLOCK_GetPeriphClkFreq</STRONG> (Thumb, 278 bytes, Stack size 24 bytes, fsl_clock.o(.text.CLOCK_GetPeriphClkFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 696<LI>Call Chain = CLOCK_GetPeriphClkFreq &rArr; CLOCK_GetSysPfdFreq &rArr; CLOCK_GetPllFreq &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetSysPfdFreq
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetPllFreq
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetOscFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetSemcFreq
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetAhbFreq
</UL>

<P><STRONG><a name="[148]"></a>CLOCK_GetOscFreq</STRONG> (Thumb, 52 bytes, Stack size 4 bytes, fsl_clock.o(.text.CLOCK_GetOscFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = CLOCK_GetOscFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetFreq
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetPerClkFreq
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetPllBypassRefClk
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetPeriphClkFreq
</UL>

<P><STRONG><a name="[149]"></a>CLOCK_GetRtcFreq</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, fsl_clock.o(.text.CLOCK_GetRtcFreq))
<BR><BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetFreq
</UL>

<P><STRONG><a name="[14b]"></a>CLOCK_GetPllUsb1SWFreq</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, fsl_clock.o(.text.CLOCK_GetPllUsb1SWFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 664<LI>Call Chain = CLOCK_GetPllUsb1SWFreq &rArr; CLOCK_GetPllFreq &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetPllFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetFreq
</UL>

<P><STRONG><a name="[14e]"></a>CLOCK_IsPllEnabled</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, fsl_clock.o(.text.CLOCK_IsPllEnabled))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CLOCK_IsPllEnabled
</UL>
<BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetPllFreq
</UL>

<P><STRONG><a name="[14d]"></a>CLOCK_GetPllBypassRefClk</STRONG> (Thumb, 52 bytes, Stack size 24 bytes, fsl_clock.o(.text.CLOCK_GetPllBypassRefClk))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = CLOCK_GetPllBypassRefClk &rArr; CLOCK_GetOscFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetOscFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetPllFreq
</UL>

<P><STRONG><a name="[14f]"></a>CLOCK_IsPllBypassed</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, fsl_clock.o(.text.CLOCK_IsPllBypassed))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CLOCK_IsPllBypassed
</UL>
<BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_GetPllFreq
</UL>

<P><STRONG><a name="[166]"></a>FLEXIO_CommonIRQHandler</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, fsl_flexio.o(.text.FLEXIO_CommonIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = FLEXIO_CommonIRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLEXIO3_DriverIRQHandler
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLEXIO2_DriverIRQHandler
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLEXIO1_DriverIRQHandler
</UL>

<P><STRONG><a name="[16c]"></a>GPIO_GetInstance</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, fsl_gpio.o(.text.GPIO_GetInstance))
<BR><BR>[Stack]<UL><LI>Max Depth = 600<LI>Call Chain = GPIO_GetInstance &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinInit
</UL>

<P><STRONG><a name="[13d]"></a>CLOCK_EnableClock</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, fsl_gpio.o(.text.CLOCK_EnableClock))
<BR><BR>[Stack]<UL><LI>Max Depth = 632<LI>Call Chain = CLOCK_EnableClock &rArr; CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_ControlGate
</UL>
<BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinInit
</UL>

<P><STRONG><a name="[170]"></a>GPIO_SetPinInterruptConfig</STRONG> (Thumb, 28 bytes, Stack size 24 bytes, fsl_gpio.o(.text.GPIO_SetPinInterruptConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = GPIO_SetPinInterruptConfig &rArr; GPIO_PinSetInterruptConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinSetInterruptConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinInit
</UL>

<P><STRONG><a name="[12e]"></a>CLOCK_ControlGate</STRONG> (Thumb, 102 bytes, Stack size 24 bytes, fsl_gpio.o(.text.CLOCK_ControlGate))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_SDK_AtomicLocalClearAndSet4Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_EnableClock
</UL>

<P><STRONG><a name="[12f]"></a>_SDK_AtomicLocalClearAndSet4Byte</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, fsl_gpio.o(.text._SDK_AtomicLocalClearAndSet4Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _SDK_AtomicLocalClearAndSet4Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_ControlGate
</UL>

<P><STRONG><a name="[176]"></a>GPT_GetInstance</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, fsl_gpt.o(.text.GPT_GetInstance))
<BR><BR>[Stack]<UL><LI>Max Depth = 600<LI>Call Chain = GPT_GetInstance &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Called By]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPT_Deinit
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPT_Init
</UL>

<P><STRONG><a name="[13e]"></a>CLOCK_EnableClock</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, fsl_gpt.o(.text.CLOCK_EnableClock))
<BR><BR>[Stack]<UL><LI>Max Depth = 632<LI>Call Chain = CLOCK_EnableClock &rArr; CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_ControlGate
</UL>
<BR>[Called By]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPT_Init
</UL>

<P><STRONG><a name="[179]"></a>GPT_SoftwareReset</STRONG> (Thumb, 34 bytes, Stack size 4 bytes, fsl_gpt.o(.text.GPT_SoftwareReset))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = GPT_SoftwareReset
</UL>
<BR>[Called By]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPT_Init
</UL>

<P><STRONG><a name="[17a]"></a>GPT_SetClockSource</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, fsl_gpt.o(.text.GPT_SetClockSource))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = GPT_SetClockSource
</UL>
<BR>[Called By]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPT_Init
</UL>

<P><STRONG><a name="[17b]"></a>GPT_SetClockDivider</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, fsl_gpt.o(.text.GPT_SetClockDivider))
<BR><BR>[Stack]<UL><LI>Max Depth = 600<LI>Call Chain = GPT_SetClockDivider &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Called By]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPT_Init
</UL>

<P><STRONG><a name="[130]"></a>CLOCK_ControlGate</STRONG> (Thumb, 102 bytes, Stack size 24 bytes, fsl_gpt.o(.text.CLOCK_ControlGate))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_SDK_AtomicLocalClearAndSet4Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_DisableClock
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_EnableClock
</UL>

<P><STRONG><a name="[13a]"></a>CLOCK_DisableClock</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, fsl_gpt.o(.text.CLOCK_DisableClock))
<BR><BR>[Stack]<UL><LI>Max Depth = 632<LI>Call Chain = CLOCK_DisableClock &rArr; CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_ControlGate
</UL>
<BR>[Called By]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPT_Deinit
</UL>

<P><STRONG><a name="[131]"></a>_SDK_AtomicLocalClearAndSet4Byte</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, fsl_gpt.o(.text._SDK_AtomicLocalClearAndSet4Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _SDK_AtomicLocalClearAndSet4Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_ControlGate
</UL>

<P><STRONG><a name="[17d]"></a>LPI2C_CommonIRQHandler</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, fsl_lpi2c.o(.text.LPI2C_CommonIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = LPI2C_CommonIRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPI2C4_DriverIRQHandler
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPI2C3_DriverIRQHandler
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPI2C2_DriverIRQHandler
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPI2C1_DriverIRQHandler
</UL>

<P><STRONG><a name="[13f]"></a>CLOCK_EnableClock</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, fsl_lpspi.o(.text.CLOCK_EnableClock))
<BR><BR>[Stack]<UL><LI>Max Depth = 632<LI>Call Chain = CLOCK_EnableClock &rArr; CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_ControlGate
</UL>
<BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterInit
</UL>

<P><STRONG><a name="[185]"></a>LPSPI_SetMasterSlaveMode</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, fsl_lpspi.o(.text.LPSPI_SetMasterSlaveMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LPSPI_SetMasterSlaveMode
</UL>
<BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterInit
</UL>

<P><STRONG><a name="[186]"></a>LPSPI_SetOnePcsPolarity</STRONG> (Thumb, 60 bytes, Stack size 12 bytes, fsl_lpspi.o(.text.LPSPI_SetOnePcsPolarity))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = LPSPI_SetOnePcsPolarity
</UL>
<BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterInit
</UL>

<P><STRONG><a name="[188]"></a>LPSPI_SetFifoWatermarks</STRONG> (Thumb, 32 bytes, Stack size 12 bytes, fsl_lpspi.o(.text.LPSPI_SetFifoWatermarks))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = LPSPI_SetFifoWatermarks
</UL>
<BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterInit
</UL>

<P><STRONG><a name="[189]"></a>LPSPI_Enable</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, fsl_lpspi.o(.text.LPSPI_Enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LPSPI_Enable
</UL>
<BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterInit
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterTransferBlocking
</UL>

<P><STRONG><a name="[132]"></a>CLOCK_ControlGate</STRONG> (Thumb, 102 bytes, Stack size 24 bytes, fsl_lpspi.o(.text.CLOCK_ControlGate))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_SDK_AtomicLocalClearAndSet4Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_EnableClock
</UL>

<P><STRONG><a name="[181]"></a>LPSPI_IsMaster</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, fsl_lpspi.o(.text.LPSPI_IsMaster))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LPSPI_IsMaster
</UL>
<BR>[Called By]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterSetBaudRate
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_CommonIRQHandler
</UL>

<P><STRONG><a name="[195]"></a>LPSPI_GetRxFifoCount</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, fsl_lpspi.o(.text.LPSPI_GetRxFifoCount))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LPSPI_GetRxFifoCount
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterTransferBlocking
</UL>

<P><STRONG><a name="[196]"></a>LPSPI_ReadData</STRONG> (Thumb, 12 bytes, Stack size 4 bytes, fsl_lpspi.o(.text.LPSPI_ReadData))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LPSPI_ReadData
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterTransferBlocking
</UL>

<P><STRONG><a name="[197]"></a>LPSPI_SeparateReadData</STRONG> (Thumb, 402 bytes, Stack size 16 bytes, fsl_lpspi.o(.text.LPSPI_SeparateReadData))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = LPSPI_SeparateReadData &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterTransferBlocking
</UL>

<P><STRONG><a name="[193]"></a>LPSPI_GetTxFifoCount</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, fsl_lpspi.o(.text.LPSPI_GetTxFifoCount))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LPSPI_GetTxFifoCount
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterTransferBlocking
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_TxFifoReady
</UL>

<P><STRONG><a name="[18e]"></a>LPSPI_GetStatusFlags</STRONG> (Thumb, 12 bytes, Stack size 4 bytes, fsl_lpspi.o(.text.LPSPI_GetStatusFlags))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LPSPI_GetStatusFlags
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterTransferBlocking
</UL>

<P><STRONG><a name="[190]"></a>LPSPI_ClearStatusFlags</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, fsl_lpspi.o(.text.LPSPI_ClearStatusFlags))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LPSPI_ClearStatusFlags
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterTransferBlocking
</UL>

<P><STRONG><a name="[18f]"></a>LPSPI_FlushFifo</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, fsl_lpspi.o(.text.LPSPI_FlushFifo))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LPSPI_FlushFifo
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterTransferBlocking
</UL>

<P><STRONG><a name="[191]"></a>LPSPI_GetRxFifoSize</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, fsl_lpspi.o(.text.LPSPI_GetRxFifoSize))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LPSPI_GetRxFifoSize
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterTransferBlocking
</UL>

<P><STRONG><a name="[192]"></a>LPSPI_TxFifoReady</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, fsl_lpspi.o(.text.LPSPI_TxFifoReady))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = LPSPI_TxFifoReady &rArr; LPSPI_GetTxFifoCount
</UL>
<BR>[Calls]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_GetTxFifoCount
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterTransferBlocking
</UL>

<P><STRONG><a name="[180]"></a>LPSPI_CombineWriteData</STRONG> (Thumb, 454 bytes, Stack size 16 bytes, fsl_lpspi.o(.text.LPSPI_CombineWriteData))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = LPSPI_CombineWriteData &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterTransferBlocking
</UL>

<P><STRONG><a name="[194]"></a>LPSPI_WriteData</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, fsl_lpspi.o(.text.LPSPI_WriteData))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LPSPI_WriteData
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_MasterTransferBlocking
</UL>

<P><STRONG><a name="[17e]"></a>LPSPI_CommonIRQHandler</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, fsl_lpspi.o(.text.LPSPI_CommonIRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = LPSPI_CommonIRQHandler &rArr; LPSPI_IsMaster
</UL>
<BR>[Calls]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI_IsMaster
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI4_DriverIRQHandler
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI3_DriverIRQHandler
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI2_DriverIRQHandler
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPSPI1_DriverIRQHandler
</UL>

<P><STRONG><a name="[133]"></a>_SDK_AtomicLocalClearAndSet4Byte</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, fsl_lpspi.o(.text._SDK_AtomicLocalClearAndSet4Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _SDK_AtomicLocalClearAndSet4Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_ControlGate
</UL>

<P><STRONG><a name="[140]"></a>CLOCK_EnableClock</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, fsl_lpuart.o(.text.CLOCK_EnableClock))
<BR><BR>[Stack]<UL><LI>Max Depth = 632<LI>Call Chain = CLOCK_EnableClock &rArr; CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_ControlGate
</UL>
<BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_Init
</UL>

<P><STRONG><a name="[1a0]"></a>LPUART_SoftwareReset</STRONG> (Thumb, 28 bytes, Stack size 4 bytes, fsl_lpuart.o(.text.LPUART_SoftwareReset))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LPUART_SoftwareReset
</UL>
<BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_Init
</UL>

<P><STRONG><a name="[134]"></a>CLOCK_ControlGate</STRONG> (Thumb, 102 bytes, Stack size 24 bytes, fsl_lpuart.o(.text.CLOCK_ControlGate))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_SDK_AtomicLocalClearAndSet4Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_DisableClock
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_EnableClock
</UL>

<P><STRONG><a name="[13b]"></a>CLOCK_DisableClock</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, fsl_lpuart.o(.text.CLOCK_DisableClock))
<BR><BR>[Stack]<UL><LI>Max Depth = 632<LI>Call Chain = CLOCK_DisableClock &rArr; CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_ControlGate
</UL>
<BR>[Called By]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART_Deinit
</UL>

<P><STRONG><a name="[135]"></a>_SDK_AtomicLocalClearAndSet4Byte</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, fsl_lpuart.o(.text._SDK_AtomicLocalClearAndSet4Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _SDK_AtomicLocalClearAndSet4Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_ControlGate
</UL>

<P><STRONG><a name="[1a2]"></a>PIT_GetInstance</STRONG> (Thumb, 86 bytes, Stack size 8 bytes, fsl_pit.o(.text.PIT_GetInstance))
<BR><BR>[Stack]<UL><LI>Max Depth = 600<LI>Call Chain = PIT_GetInstance &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Called By]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PIT_Deinit
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PIT_Init
</UL>

<P><STRONG><a name="[141]"></a>CLOCK_EnableClock</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, fsl_pit.o(.text.CLOCK_EnableClock))
<BR><BR>[Stack]<UL><LI>Max Depth = 632<LI>Call Chain = CLOCK_EnableClock &rArr; CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_ControlGate
</UL>
<BR>[Called By]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PIT_Init
</UL>

<P><STRONG><a name="[136]"></a>CLOCK_ControlGate</STRONG> (Thumb, 102 bytes, Stack size 24 bytes, fsl_pit.o(.text.CLOCK_ControlGate))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_SDK_AtomicLocalClearAndSet4Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_DisableClock
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_EnableClock
</UL>

<P><STRONG><a name="[13c]"></a>CLOCK_DisableClock</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, fsl_pit.o(.text.CLOCK_DisableClock))
<BR><BR>[Stack]<UL><LI>Max Depth = 632<LI>Call Chain = CLOCK_DisableClock &rArr; CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_ControlGate
</UL>
<BR>[Called By]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PIT_Deinit
</UL>

<P><STRONG><a name="[137]"></a>_SDK_AtomicLocalClearAndSet4Byte</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, fsl_pit.o(.text._SDK_AtomicLocalClearAndSet4Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _SDK_AtomicLocalClearAndSet4Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_ControlGate
</UL>

<P><STRONG><a name="[1aa]"></a>PWM_GetComplementU16</STRONG> (Thumb, 18 bytes, Stack size 4 bytes, fsl_pwm.o(.text.PWM_GetComplementU16))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = PWM_GetComplementU16
</UL>
<BR>[Called By]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_UpdatePwmDutycycleHighAccuracy
</UL>

<P><STRONG><a name="[1ab]"></a>SAI_RxGetEnabledInterruptStatus</STRONG> (Thumb, 56 bytes, Stack size 20 bytes, fsl_sai.o(.text.SAI_RxGetEnabledInterruptStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SAI_RxGetEnabledInterruptStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SAI2_DriverIRQHandler
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SAI1_DriverIRQHandler
</UL>

<P><STRONG><a name="[1ac]"></a>SAI_TxGetEnabledInterruptStatus</STRONG> (Thumb, 54 bytes, Stack size 20 bytes, fsl_sai.o(.text.SAI_TxGetEnabledInterruptStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SAI_TxGetEnabledInterruptStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SAI2_DriverIRQHandler
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SAI1_DriverIRQHandler
</UL>

<P><STRONG><a name="[127]"></a>CLOCK_GetMux</STRONG> (Thumb, 40 bytes, Stack size 4 bytes, board.o(.text.CLOCK_GetMux))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = CLOCK_GetMux
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BOARD_DebugConsoleSrcFreq
</UL>

<P><STRONG><a name="[129]"></a>CLOCK_GetDiv</STRONG> (Thumb, 40 bytes, Stack size 4 bytes, board.o(.text.CLOCK_GetDiv))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = CLOCK_GetDiv
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BOARD_DebugConsoleSrcFreq
</UL>

<P><STRONG><a name="[12a]"></a>CLOCK_GetOscFreq</STRONG> (Thumb, 52 bytes, Stack size 4 bytes, board.o(.text.CLOCK_GetOscFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = CLOCK_GetOscFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BOARD_DebugConsoleSrcFreq
</UL>

<P><STRONG><a name="[124]"></a>ARM_MPU_Disable</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, board.o(.text.ARM_MPU_Disable))
<BR><BR>[Called By]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BOARD_ConfigMPU
</UL>

<P><STRONG><a name="[125]"></a>ARM_MPU_Enable</STRONG> (Thumb, 48 bytes, Stack size 4 bytes, board.o(.text.ARM_MPU_Enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = ARM_MPU_Enable
</UL>
<BR>[Called By]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BOARD_ConfigMPU
</UL>

<P><STRONG><a name="[115]"></a>CLOCK_SetRtcXtalFreq</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, clock_config.o(.text.CLOCK_SetRtcXtalFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = CLOCK_SetRtcXtalFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BOARD_BootClockRUN
</UL>

<P><STRONG><a name="[116]"></a>CLOCK_SetXtalFreq</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, clock_config.o(.text.CLOCK_SetXtalFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = CLOCK_SetXtalFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BOARD_BootClockRUN
</UL>

<P><STRONG><a name="[11a]"></a>CLOCK_SetMux</STRONG> (Thumb, 138 bytes, Stack size 24 bytes, clock_config.o(.text.CLOCK_SetMux))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = CLOCK_SetMux &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BOARD_BootClockRUN
</UL>

<P><STRONG><a name="[11d]"></a>CLOCK_SetPllBypass</STRONG> (Thumb, 64 bytes, Stack size 12 bytes, clock_config.o(.text.CLOCK_SetPllBypass))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = CLOCK_SetPllBypass
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BOARD_BootClockRUN
</UL>

<P><STRONG><a name="[121]"></a>CLOCK_SetDiv</STRONG> (Thumb, 138 bytes, Stack size 24 bytes, clock_config.o(.text.CLOCK_SetDiv))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = CLOCK_SetDiv &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BOARD_BootClockRUN
</UL>

<P><STRONG><a name="[122]"></a>CLOCK_DisableClock</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, clock_config.o(.text.CLOCK_DisableClock))
<BR><BR>[Stack]<UL><LI>Max Depth = 632<LI>Call Chain = CLOCK_DisableClock &rArr; CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_ControlGate
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BOARD_BootClockRUN
</UL>

<P><STRONG><a name="[138]"></a>CLOCK_ControlGate</STRONG> (Thumb, 102 bytes, Stack size 24 bytes, clock_config.o(.text.CLOCK_ControlGate))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = CLOCK_ControlGate &rArr; __aeabi_assert &rArr; DbgConsole_Printf &rArr; DbgConsole_Vprintf &rArr; DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_SDK_AtomicLocalClearAndSet4Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_DisableClock
</UL>

<P><STRONG><a name="[139]"></a>_SDK_AtomicLocalClearAndSet4Byte</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, clock_config.o(.text._SDK_AtomicLocalClearAndSet4Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _SDK_AtomicLocalClearAndSet4Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLOCK_ControlGate
</UL>

<P><STRONG><a name="[15b]"></a>DbgConsole_PrintfFormattedData</STRONG> (Thumb, 2616 bytes, Stack size 184 bytes, fsl_debug_console.o(.text.DbgConsole_PrintfFormattedData))
<BR><BR>[Stack]<UL><LI>Max Depth = 520<LI>Call Chain = DbgConsole_PrintfFormattedData &rArr; DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DbgConsole_ConvertFloatRadixNumToString
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DbgConsole_PrintfPaddingCharacter
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DbgConsole_ConvertRadixNumToString
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DbgConsole_Vprintf
</UL>

<P><STRONG><a name="[158]"></a>DbgConsole_ConvertRadixNumToString</STRONG> (Thumb, 492 bytes, Stack size 112 bytes, fsl_debug_console.o(.text.DbgConsole_ConvertRadixNumToString))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = DbgConsole_ConvertRadixNumToString &rArr; __aeabi_ldivmod &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ldivmod
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DbgConsole_PrintfFormattedData
</UL>

<P><STRONG><a name="[15c]"></a>DbgConsole_PrintfPaddingCharacter</STRONG> (Thumb, 64 bytes, Stack size 32 bytes, fsl_debug_console.o(.text.DbgConsole_PrintfPaddingCharacter))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DbgConsole_PrintfPaddingCharacter
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DbgConsole_PrintfFormattedData
</UL>

<P><STRONG><a name="[155]"></a>DbgConsole_ConvertFloatRadixNumToString</STRONG> (Thumb, 596 bytes, Stack size 120 bytes, fsl_debug_console.o(.text.DbgConsole_ConvertFloatRadixNumToString))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = DbgConsole_ConvertFloatRadixNumToString &rArr; __hardfp_pow &rArr; sqrt
</UL>
<BR>[Calls]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_modf
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DbgConsole_PrintfFormattedData
</UL>

<P><STRONG><a name="[1b0]"></a>USB_DeviceEhciInterruptReset</STRONG> (Thumb, 140 bytes, Stack size 32 bytes, usb_device_ehci.o(.text.USB_DeviceEhciInterruptReset))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = USB_DeviceEhciInterruptReset &rArr; USB_DeviceNotificationTrigger &rArr; USB_DeviceNotification &rArr; USB_DeviceResetNotification &rArr; USB_DeviceControl
</UL>
<BR>[Calls]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeviceNotificationTrigger
</UL>
<BR>[Called By]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeviceEhciIsrFunction
</UL>

<P><STRONG><a name="[1b1]"></a>USB_DeviceEhciInterruptTokenDone</STRONG> (Thumb, 1050 bytes, Stack size 56 bytes, usb_device_ehci.o(.text.USB_DeviceEhciInterruptTokenDone))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = USB_DeviceEhciInterruptTokenDone &rArr; USB_DeviceEhciCancelControlPipe &rArr; USB_DeviceNotificationTrigger &rArr; USB_DeviceNotification &rArr; USB_DeviceResetNotification &rArr; USB_DeviceControl
</UL>
<BR>[Calls]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeviceNotificationTrigger
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeviceEhciFillSetupBuffer
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeviceEhciCancelControlPipe
</UL>
<BR>[Called By]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeviceEhciIsrFunction
</UL>

<P><STRONG><a name="[1af]"></a>USB_DeviceEhciInterruptPortChange</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, usb_device_ehci.o(.text.USB_DeviceEhciInterruptPortChange))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = USB_DeviceEhciInterruptPortChange &rArr; USB_DeviceNotificationTrigger &rArr; USB_DeviceNotification &rArr; USB_DeviceResetNotification &rArr; USB_DeviceControl
</UL>
<BR>[Calls]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeviceNotificationTrigger
</UL>
<BR>[Called By]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeviceEhciIsrFunction
</UL>

<P><STRONG><a name="[1b4]"></a>USB_DeviceEhciInterruptSof</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, usb_device_ehci.o(.text.USB_DeviceEhciInterruptSof))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = USB_DeviceEhciInterruptSof
</UL>
<BR>[Called By]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeviceEhciIsrFunction
</UL>

<P><STRONG><a name="[1ad]"></a>USB_DeviceEhciCancelControlPipe</STRONG> (Thumb, 348 bytes, Stack size 48 bytes, usb_device_ehci.o(.text.USB_DeviceEhciCancelControlPipe))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = USB_DeviceEhciCancelControlPipe &rArr; USB_DeviceNotificationTrigger &rArr; USB_DeviceNotification &rArr; USB_DeviceResetNotification &rArr; USB_DeviceControl
</UL>
<BR>[Calls]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeviceNotificationTrigger
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeviceEhciInterruptTokenDone
</UL>

<P><STRONG><a name="[1b2]"></a>USB_DeviceEhciFillSetupBuffer</STRONG> (Thumb, 142 bytes, Stack size 8 bytes, usb_device_ehci.o(.text.USB_DeviceEhciFillSetupBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USB_DeviceEhciFillSetupBuffer
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeviceEhciInterruptTokenDone
</UL>

<P><STRONG><a name="[1b5]"></a>USB_DeviceNotification</STRONG> (Thumb, 236 bytes, Stack size 32 bytes, usb_device_dci.o(.text.USB_DeviceNotification))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = USB_DeviceNotification &rArr; USB_DeviceResetNotification &rArr; USB_DeviceControl
</UL>
<BR>[Calls]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeviceResetNotification
</UL>
<BR>[Called By]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeviceNotificationTrigger
</UL>

<P><STRONG><a name="[1b6]"></a>USB_DeviceResetNotification</STRONG> (Thumb, 134 bytes, Stack size 32 bytes, usb_device_dci.o(.text.USB_DeviceResetNotification))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = USB_DeviceResetNotification &rArr; USB_DeviceControl
</UL>
<BR>[Calls]<UL><LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeviceControl
</UL>
<BR>[Called By]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeviceNotification
</UL>

<P><STRONG><a name="[1b7]"></a>USB_DeviceControl</STRONG> (Thumb, 86 bytes, Stack size 32 bytes, usb_device_dci.o(.text.USB_DeviceControl))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = USB_DeviceControl
</UL>
<BR>[Called By]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeviceResetNotification
</UL>

<P><STRONG><a name="[21d]"></a>_fp_digits</STRONG> (Thumb, 366 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[210]"></a>_printf_core</STRONG> (Thumb, 1744 bytes, Stack size 136 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
<LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf
</UL>

<P><STRONG><a name="[220]"></a>_printf_post_padding</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[21f]"></a>_printf_pre_padding</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[e9]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printfa.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0sprintf)
</UL>
<P><STRONG><a name="[f7]"></a>_fp_value</STRONG> (Thumb, 296 bytes, Stack size 64 bytes, scanf_fp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ul2d
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[e2]"></a>_scanf_char_input</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, scanf_char.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> scanf_char.o(.text)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
