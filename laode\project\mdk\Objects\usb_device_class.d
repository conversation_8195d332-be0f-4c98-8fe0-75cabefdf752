./objects/usb_device_class.o: \
  ..\..\libraries\components\usb\usb_cdc_adapter\usb_device_class.c \
  ..\..\libraries\components\usb\usb_cdc_adapter\usb_device_config.h \
  ..\..\libraries\components\usb\include\usb.h \
  ..\..\libraries\sdk\drives\fsl_common.h \
  ..\..\libraries\sdk\deceive\fsl_device_registers.h \
  ..\..\libraries\sdk\deceive\MIMXRT1064.h \
  ..\..\libraries\sdk\CMSIS\Include\core_cm7.h \
  ..\..\libraries\sdk\deceive\system_MIMXRT1064.h \
  ..\..\libraries\sdk\deceive\MIMXRT1064_features.h \
  ..\..\libraries\sdk\drives\fsl_common_arm.h \
  ..\..\libraries\sdk\drives\fsl_clock.h \
  ..\..\libraries\sdk\components\osa\fsl_os_abstraction.h \
  ..\..\libraries\sdk\components\osa\fsl_os_abstraction_config.h \
  ..\..\libraries\sdk\components\lists\fsl_component_generic_list.h \
  ..\..\libraries\sdk\components\osa\fsl_os_abstraction_bm.h \
  ..\..\libraries\components\usb\include\usb_misc.h \
  ..\..\libraries\components\usb\include\usb_spec.h \
  ..\..\libraries\components\usb\device\usb_device.h \
  ..\..\libraries\components\usb\usb_cdc_adapter\usb_device_ch9.h \
  ..\..\libraries\components\usb\usb_cdc_adapter\usb_device_class.h \
  ..\..\libraries\components\usb\usb_cdc_adapter\usb_device_cdc_acm.h
