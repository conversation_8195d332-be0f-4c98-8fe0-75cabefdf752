<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>nor_sdram_zf_dtcm</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>6220000::V6.22::ARMCLANG</pCCUsed>
      <uAC6>1</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>MIMXRT1064DVL6A</Device>
          <Vendor>NXP</Vendor>
          <PackID>NXP.MIMXRT1064_DFP.15.0.0</PackID>
          <PackURL>https://mcuxpresso.nxp.com/cmsis_pack/repo/</PackURL>
          <Cpu>IRAM(0x20000000,0x020000) IRAM2(0x00000000,0x020000) IROM(0x70000000,0x400000) XRAM(0x20200000,0x0c0000) CPUTYPE("Cortex-M7") FPU3(DFPU) CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll></FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:MIMXRT1064DVL6A$fsl_device_registers.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:MIMXRT1064DVL6A$MIMXRT1064.xml</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\Objects\</OutputDirectory>
          <OutputName>rt1064</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>0</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\Listings\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>fromelf.exe --bincombined  --output  "$<EMAIL>"  "#L"</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> </SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM7</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments></TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM7</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M7"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>1</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>3</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <nBranchProt>0</nBranchProt>
            <hadIRAM2>1</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>4</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x70000000</StartAddress>
                <Size>0x400000</Size>
              </IROM>
              <XRAM>
                <Type>1</Type>
                <StartAddress>0x20200000</StartAddress>
                <Size>0xc0000</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x70000000</StartAddress>
                <Size>0x400000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x20200000</StartAddress>
                <Size>0xc0000</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>1</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>1</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>3</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>1</uSurpInc>
            <uC99>1</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>3</v6Lang>
            <v6LangP>3</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls>-fno-common  -fdata-sections  -ffreestanding  -fno-builtin  -mthumb -Wno-invalid-source-encoding</MiscControls>
              <Define>CPU_MIMXRT1064DVL6A,SKIP_SYSCLK_INIT,XIP_EXTERNAL_FLASH=1, XIP_BOOT_HEADER_DCD_ENABLE =1, XIP_BOOT_HEADER_ENABLE =1,PRINTF_FLOAT_ENABLE=1, SCANF_FLOAT_ENABLE=1, PRINTF_ADVANCED_ENABLE=1, SCANF_ADVANCED_ENABLE=1, FSL_DRIVER_TRANSFER_DOUBLE_WEAK_IRQ=0, USB_STACK_BM, DEBUG, MCUXPRESSO_SDK, SD_ENABLED</Define>
              <Undefine></Undefine>
              <IncludePath>..\user\inc;..\code;..\..\libraries\sdk\startup\mdk;..\..\libraries\sdk\deceive;..\..\libraries\sdk\drives;..\..\libraries\sdk\xip;..\..\libraries\sdk\utilities;..\..\libraries\sdk\utilities\debug_console;..\..\libraries\sdk\utilities\str;..\..\libraries\sdk\cmsis_drivers;..\..\libraries\sdk\CMSIS\Include;..\..\libraries\sdk\CMSIS\Driver\Include;..\..\libraries\sdk\components\lists;..\..\libraries\sdk\components\osa;..\..\libraries\sdk\components\serial_manager;..\..\libraries\sdk\components\uart;..\..\libraries\sdk\board;..\..\libraries\components\fatfs\source;..\..\libraries\components\fatfs\source\fsl_sd_disk;..\..\libraries\components\sdmmc;..\..\libraries\components\sdmmc\common;..\..\libraries\components\sdmmc\host\usdhc;..\..\libraries\components\sdmmc\osa;..\..\libraries\components\sdmmc\sd;..\..\libraries\components\usb\device;..\..\libraries\components\usb\include;..\..\libraries\components\usb\phy;..\..\libraries\components\usb\usb_cdc_adapter;..\..\libraries\zf_common;..\..\libraries\zf_driver;..\..\libraries\zf_device;..\..\libraries\zf_components</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>1</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>DEBUG, __STARTUP_INITIALIZE_NONCACHEDATA</Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>0</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile>.\scf\MIMXRT1064xxxxx_flexspi_nor.scf</ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc>--remove --entry=Reset_Handler</Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings>6314,6329</DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>user_c</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\src\main.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>user_h</GroupName>
          <Files>
            <File>
              <FileName>isr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\src\isr.c</FilePath>
            </File>
            <File>
              <FileName>isr.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\user\inc\isr.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>code</GroupName>
          <Files>
            <File>
              <FileName>Serial.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\code\Serial.c</FilePath>
            </File>
            <File>
              <FileName>Serial.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\code\Serial.h</FilePath>
            </File>
            <File>
              <FileName>system_init.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\code\system_init.c</FilePath>
            </File>
            <File>
              <FileName>system_init.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\code\system_init.h</FilePath>
            </File>
            <File>
              <FileName>swj.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\code\swj.c</FilePath>
            </File>
            <File>
              <FileName>swj.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\code\swj.h</FilePath>
            </File>
            <File>
              <FileName>vofa.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\code\vofa.c</FilePath>
            </File>
            <File>
              <FileName>vofa.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\code\vofa.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>doc</GroupName>
          <Files>
            <File>
              <FileName>GPL3_permission_statement.txt</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\doc\GPL3_permission_statement.txt</FilePath>
            </File>
            <File>
              <FileName>read me.txt</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\doc\read me.txt</FilePath>
            </File>
            <File>
              <FileName>version.txt</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\doc\version.txt</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>zf_common</GroupName>
          <Files>
            <File>
              <FileName>zf_common_clock.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_common\zf_common_clock.c</FilePath>
            </File>
            <File>
              <FileName>zf_common_clock.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_common\zf_common_clock.h</FilePath>
            </File>
            <File>
              <FileName>zf_common_debug.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_common\zf_common_debug.c</FilePath>
            </File>
            <File>
              <FileName>zf_common_debug.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_common\zf_common_debug.h</FilePath>
            </File>
            <File>
              <FileName>zf_common_fifo.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_common\zf_common_fifo.c</FilePath>
            </File>
            <File>
              <FileName>zf_common_fifo.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_common\zf_common_fifo.h</FilePath>
            </File>
            <File>
              <FileName>zf_common_font.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_common\zf_common_font.c</FilePath>
            </File>
            <File>
              <FileName>zf_common_font.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_common\zf_common_font.h</FilePath>
            </File>
            <File>
              <FileName>zf_common_function.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_common\zf_common_function.c</FilePath>
            </File>
            <File>
              <FileName>zf_common_function.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_common\zf_common_function.h</FilePath>
            </File>
            <File>
              <FileName>zf_common_headfile.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_common\zf_common_headfile.h</FilePath>
            </File>
            <File>
              <FileName>zf_common_interrupt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_common\zf_common_interrupt.c</FilePath>
            </File>
            <File>
              <FileName>zf_common_interrupt.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_common\zf_common_interrupt.h</FilePath>
            </File>
            <File>
              <FileName>zf_common_typedef.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_common\zf_common_typedef.h</FilePath>
            </File>
            <File>
              <FileName>zf_common_vector.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_common\zf_common_vector.c</FilePath>
            </File>
            <File>
              <FileName>zf_common_vector.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_common\zf_common_vector.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>zf_driver</GroupName>
          <Files>
            <File>
              <FileName>zf_driver_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_adc.c</FilePath>
            </File>
            <File>
              <FileName>zf_driver_adc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_adc.h</FilePath>
            </File>
            <File>
              <FileName>zf_driver_csi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_csi.c</FilePath>
            </File>
            <File>
              <FileName>zf_driver_csi.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_csi.h</FilePath>
            </File>
            <File>
              <FileName>zf_driver_delay.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_delay.c</FilePath>
            </File>
            <File>
              <FileName>zf_driver_delay.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_delay.h</FilePath>
            </File>
            <File>
              <FileName>zf_driver_encoder.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_encoder.c</FilePath>
            </File>
            <File>
              <FileName>zf_driver_encoder.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_encoder.h</FilePath>
            </File>
            <File>
              <FileName>zf_driver_exti.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_exti.c</FilePath>
            </File>
            <File>
              <FileName>zf_driver_exti.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_exti.h</FilePath>
            </File>
            <File>
              <FileName>zf_driver_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_flash.c</FilePath>
            </File>
            <File>
              <FileName>zf_driver_flash.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_flash.h</FilePath>
            </File>
            <File>
              <FileName>zf_driver_flexio_csi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_flexio_csi.c</FilePath>
            </File>
            <File>
              <FileName>zf_driver_flexio_csi.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_flexio_csi.h</FilePath>
            </File>
            <File>
              <FileName>zf_driver_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_gpio.c</FilePath>
            </File>
            <File>
              <FileName>zf_driver_gpio.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_gpio.h</FilePath>
            </File>
            <File>
              <FileName>zf_driver_iic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_iic.c</FilePath>
            </File>
            <File>
              <FileName>zf_driver_iic.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_iic.h</FilePath>
            </File>
            <File>
              <FileName>zf_driver_pit.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_pit.c</FilePath>
            </File>
            <File>
              <FileName>zf_driver_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_uart.c</FilePath>
            </File>
            <File>
              <FileName>zf_driver_pit.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_pit.h</FilePath>
            </File>
            <File>
              <FileName>zf_driver_pwm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_pwm.c</FilePath>
            </File>
            <File>
              <FileName>zf_driver_pwm.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_pwm.h</FilePath>
            </File>
            <File>
              <FileName>zf_driver_romapi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_romapi.c</FilePath>
            </File>
            <File>
              <FileName>zf_driver_romapi.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_romapi.h</FilePath>
            </File>
            <File>
              <FileName>zf_driver_sdio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_sdio.c</FilePath>
            </File>
            <File>
              <FileName>zf_driver_sdio.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_sdio.h</FilePath>
            </File>
            <File>
              <FileName>zf_driver_soft_iic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_soft_iic.c</FilePath>
            </File>
            <File>
              <FileName>zf_driver_soft_iic.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_soft_iic.h</FilePath>
            </File>
            <File>
              <FileName>zf_driver_soft_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_soft_spi.c</FilePath>
            </File>
            <File>
              <FileName>zf_driver_soft_spi.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_soft_spi.h</FilePath>
            </File>
            <File>
              <FileName>zf_driver_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_spi.c</FilePath>
            </File>
            <File>
              <FileName>zf_driver_spi.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_spi.h</FilePath>
            </File>
            <File>
              <FileName>zf_driver_timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_timer.c</FilePath>
            </File>
            <File>
              <FileName>zf_driver_timer.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_timer.h</FilePath>
            </File>
            <File>
              <FileName>zf_driver_uart.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_uart.h</FilePath>
            </File>
            <File>
              <FileName>zf_driver_usb_cdc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_usb_cdc.c</FilePath>
            </File>
            <File>
              <FileName>zf_driver_usb_cdc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_driver\zf_driver_usb_cdc.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>zf_device</GroupName>
          <Files>
            <File>
              <FileName>zf_device_absolute_encoder.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_absolute_encoder.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_absolute_encoder.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_absolute_encoder.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_bluetooth_ch9141.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_bluetooth_ch9141.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_bluetooth_ch9141.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_bluetooth_ch9141.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_camera.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_camera.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_camera.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_camera.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_gps_tau1201.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_gps_tau1201.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_gps_tau1201.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_gps_tau1201.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_icm20602.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_icm20602.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_icm20602.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_icm20602.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_imu963ra.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_imu963ra.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_imu963ra.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_imu963ra.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_imu660ra.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_imu660ra.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_imu660ra.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_imu660ra.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_ips114.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_ips114.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_mt9v03x.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_mt9v03x.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_ips114.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_ips114.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_ips200.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_ips200.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_ips200.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_ips200.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_key.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_key.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_key.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_key.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_mpu6050.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_mpu6050.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_mpu6050.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_mpu6050.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_mt9v03x.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_mt9v03x.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_mt9v03x_flexio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_mt9v03x_flexio.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_mt9v03x_flexio.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_mt9v03x_flexio.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_oled.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_oled.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_oled.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_oled.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_ov7725.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_ov7725.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_ov7725.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_ov7725.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_scc8660.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_scc8660.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_scc8660.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_scc8660.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_scc8660_flexio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_scc8660_flexio.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_scc8660_flexio.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_scc8660_flexio.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_tft180.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_tft180.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_tft180.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_tft180.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_tsl1401.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_tsl1401.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_tsl1401.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_tsl1401.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_type.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_type.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_type.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_type.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_virtual_oscilloscope.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_virtual_oscilloscope.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_virtual_oscilloscope.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_virtual_oscilloscope.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_wifi_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_wifi_uart.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_wifi_uart.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_wifi_uart.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_wireless_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_wireless_uart.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_wireless_uart.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_wireless_uart.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_config.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_config.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_config.lib</FilePath>
            </File>
            <File>
              <FileName>zf_device_dl1a.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_dl1a.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_dl1a.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_dl1a.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_dl1b.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_dl1b.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_dl1b.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_dl1b.h</FilePath>
            </File>
            <File>
              <FileName>zf_device_wifi_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_wifi_spi.c</FilePath>
            </File>
            <File>
              <FileName>zf_device_wifi_spi.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_device\zf_device_wifi_spi.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>zf_components</GroupName>
          <Files>
            <File>
              <FileName>seekfree_assistant.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_components\seekfree_assistant.c</FilePath>
            </File>
            <File>
              <FileName>seekfree_assistant.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\zf_components\seekfree_assistant.h</FilePath>
            </File>
            <File>
              <FileName>seekfree_assistant_interface.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\zf_components\seekfree_assistant_interface.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>sdk_drivers</GroupName>
          <Files>
            <File>
              <FileName>fsl_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_adc.c</FilePath>
            </File>
            <File>
              <FileName>fsl_adc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_adc.h</FilePath>
            </File>
            <File>
              <FileName>fsl_adc_etc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_adc_etc.c</FilePath>
            </File>
            <File>
              <FileName>fsl_adc_etc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_adc_etc.h</FilePath>
            </File>
            <File>
              <FileName>fsl_aipstz.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_aipstz.c</FilePath>
            </File>
            <File>
              <FileName>fsl_aipstz.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_aipstz.h</FilePath>
            </File>
            <File>
              <FileName>fsl_aoi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_aoi.c</FilePath>
            </File>
            <File>
              <FileName>fsl_aoi.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_aoi.h</FilePath>
            </File>
            <File>
              <FileName>fsl_bee.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_bee.c</FilePath>
            </File>
            <File>
              <FileName>fsl_bee.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_bee.h</FilePath>
            </File>
            <File>
              <FileName>fsl_cache.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_cache.c</FilePath>
            </File>
            <File>
              <FileName>fsl_cache.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_cache.h</FilePath>
            </File>
            <File>
              <FileName>fsl_clock.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_clock.c</FilePath>
            </File>
            <File>
              <FileName>fsl_clock.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_clock.h</FilePath>
            </File>
            <File>
              <FileName>fsl_cmp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_cmp.c</FilePath>
            </File>
            <File>
              <FileName>fsl_cmp.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_cmp.h</FilePath>
            </File>
            <File>
              <FileName>fsl_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_common.c</FilePath>
            </File>
            <File>
              <FileName>fsl_common.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_common.h</FilePath>
            </File>
            <File>
              <FileName>fsl_common_arm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_common_arm.c</FilePath>
            </File>
            <File>
              <FileName>fsl_common_arm.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_common_arm.h</FilePath>
            </File>
            <File>
              <FileName>fsl_csi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_csi.c</FilePath>
            </File>
            <File>
              <FileName>fsl_csi.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_csi.h</FilePath>
            </File>
            <File>
              <FileName>fsl_dcdc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_dcdc.c</FilePath>
            </File>
            <File>
              <FileName>fsl_dcdc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_dcdc.h</FilePath>
            </File>
            <File>
              <FileName>fsl_dcp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_dcp.c</FilePath>
            </File>
            <File>
              <FileName>fsl_dcp.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_dcp.h</FilePath>
            </File>
            <File>
              <FileName>fsl_dmamux.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_dmamux.c</FilePath>
            </File>
            <File>
              <FileName>fsl_dmamux.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_dmamux.h</FilePath>
            </File>
            <File>
              <FileName>fsl_edma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_edma.c</FilePath>
            </File>
            <File>
              <FileName>fsl_edma.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_edma.h</FilePath>
            </File>
            <File>
              <FileName>fsl_elcdif.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_elcdif.c</FilePath>
            </File>
            <File>
              <FileName>fsl_elcdif.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_elcdif.h</FilePath>
            </File>
            <File>
              <FileName>fsl_enc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_enc.c</FilePath>
            </File>
            <File>
              <FileName>fsl_enc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_enc.h</FilePath>
            </File>
            <File>
              <FileName>fsl_enet.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_enet.c</FilePath>
            </File>
            <File>
              <FileName>fsl_enet.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_enet.h</FilePath>
            </File>
            <File>
              <FileName>fsl_ewm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_ewm.c</FilePath>
            </File>
            <File>
              <FileName>fsl_ewm.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_ewm.h</FilePath>
            </File>
            <File>
              <FileName>fsl_flexcan.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexcan.c</FilePath>
            </File>
            <File>
              <FileName>fsl_flexcan.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexcan.h</FilePath>
            </File>
            <File>
              <FileName>fsl_flexcan_edma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexcan_edma.c</FilePath>
            </File>
            <File>
              <FileName>fsl_flexcan_edma.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexcan_edma.h</FilePath>
            </File>
            <File>
              <FileName>fsl_flexio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexio.c</FilePath>
            </File>
            <File>
              <FileName>fsl_flexio.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexio.h</FilePath>
            </File>
            <File>
              <FileName>fsl_flexio_camera.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexio_camera.c</FilePath>
            </File>
            <File>
              <FileName>fsl_flexio_camera.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexio_camera.h</FilePath>
            </File>
            <File>
              <FileName>fsl_flexio_camera_edma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexio_camera_edma.c</FilePath>
            </File>
            <File>
              <FileName>fsl_flexio_camera_edma.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexio_camera_edma.h</FilePath>
            </File>
            <File>
              <FileName>fsl_flexio_i2c_master.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexio_i2c_master.c</FilePath>
            </File>
            <File>
              <FileName>fsl_flexio_i2c_master.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexio_i2c_master.h</FilePath>
            </File>
            <File>
              <FileName>fsl_flexio_i2s.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexio_i2s.c</FilePath>
            </File>
            <File>
              <FileName>fsl_flexio_i2s.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexio_i2s.h</FilePath>
            </File>
            <File>
              <FileName>fsl_flexio_i2s_edma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexio_i2s_edma.c</FilePath>
            </File>
            <File>
              <FileName>fsl_flexio_i2s_edma.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexio_i2s_edma.h</FilePath>
            </File>
            <File>
              <FileName>fsl_flexio_mculcd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexio_mculcd.c</FilePath>
            </File>
            <File>
              <FileName>fsl_flexio_mculcd.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexio_mculcd.h</FilePath>
            </File>
            <File>
              <FileName>fsl_flexio_mculcd_edma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexio_mculcd_edma.c</FilePath>
            </File>
            <File>
              <FileName>fsl_flexio_mculcd_edma.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexio_mculcd_edma.h</FilePath>
            </File>
            <File>
              <FileName>fsl_flexio_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexio_spi.c</FilePath>
            </File>
            <File>
              <FileName>fsl_flexio_spi.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexio_spi.h</FilePath>
            </File>
            <File>
              <FileName>fsl_flexio_spi_edma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexio_spi_edma.c</FilePath>
            </File>
            <File>
              <FileName>fsl_flexio_spi_edma.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexio_spi_edma.h</FilePath>
            </File>
            <File>
              <FileName>fsl_flexio_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexio_uart.c</FilePath>
            </File>
            <File>
              <FileName>fsl_flexio_uart.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexio_uart.h</FilePath>
            </File>
            <File>
              <FileName>fsl_flexio_uart_edma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexio_uart_edma.c</FilePath>
            </File>
            <File>
              <FileName>fsl_flexio_uart_edma.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexio_uart_edma.h</FilePath>
            </File>
            <File>
              <FileName>fsl_flexram.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexram.c</FilePath>
            </File>
            <File>
              <FileName>fsl_flexram.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexram.h</FilePath>
            </File>
            <File>
              <FileName>fsl_flexram_allocate.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexram_allocate.c</FilePath>
            </File>
            <File>
              <FileName>fsl_flexram_allocate.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexram_allocate.h</FilePath>
            </File>
            <File>
              <FileName>fsl_flexspi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexspi.c</FilePath>
            </File>
            <File>
              <FileName>fsl_flexspi.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_flexspi.h</FilePath>
            </File>
            <File>
              <FileName>fsl_gpc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_gpc.c</FilePath>
            </File>
            <File>
              <FileName>fsl_gpc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_gpc.h</FilePath>
            </File>
            <File>
              <FileName>fsl_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_gpio.c</FilePath>
            </File>
            <File>
              <FileName>fsl_gpio.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_gpio.h</FilePath>
            </File>
            <File>
              <FileName>fsl_gpt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_gpt.c</FilePath>
            </File>
            <File>
              <FileName>fsl_gpt.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_gpt.h</FilePath>
            </File>
            <File>
              <FileName>fsl_iomuxc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_iomuxc.h</FilePath>
            </File>
            <File>
              <FileName>fsl_kpp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_kpp.c</FilePath>
            </File>
            <File>
              <FileName>fsl_kpp.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_kpp.h</FilePath>
            </File>
            <File>
              <FileName>fsl_lpi2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_lpi2c.c</FilePath>
            </File>
            <File>
              <FileName>fsl_lpi2c.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_lpi2c.h</FilePath>
            </File>
            <File>
              <FileName>fsl_lpi2c_edma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_lpi2c_edma.c</FilePath>
            </File>
            <File>
              <FileName>fsl_lpi2c_edma.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_lpi2c_edma.h</FilePath>
            </File>
            <File>
              <FileName>fsl_lpspi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_lpspi.c</FilePath>
            </File>
            <File>
              <FileName>fsl_lpspi.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_lpspi.h</FilePath>
            </File>
            <File>
              <FileName>fsl_lpspi_edma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_lpspi_edma.c</FilePath>
            </File>
            <File>
              <FileName>fsl_lpspi_edma.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_lpspi_edma.h</FilePath>
            </File>
            <File>
              <FileName>fsl_lpuart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_lpuart.c</FilePath>
            </File>
            <File>
              <FileName>fsl_lpuart.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_lpuart.h</FilePath>
            </File>
            <File>
              <FileName>fsl_lpuart_edma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_lpuart_edma.c</FilePath>
            </File>
            <File>
              <FileName>fsl_lpuart_edma.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_lpuart_edma.h</FilePath>
            </File>
            <File>
              <FileName>fsl_ocotp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_ocotp.c</FilePath>
            </File>
            <File>
              <FileName>fsl_ocotp.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_ocotp.h</FilePath>
            </File>
            <File>
              <FileName>fsl_pit.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_pit.c</FilePath>
            </File>
            <File>
              <FileName>fsl_pit.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_pit.h</FilePath>
            </File>
            <File>
              <FileName>fsl_pmu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_pmu.c</FilePath>
            </File>
            <File>
              <FileName>fsl_pmu.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_pmu.h</FilePath>
            </File>
            <File>
              <FileName>fsl_pwm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_pwm.c</FilePath>
            </File>
            <File>
              <FileName>fsl_pwm.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_pwm.h</FilePath>
            </File>
            <File>
              <FileName>fsl_pxp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_pxp.c</FilePath>
            </File>
            <File>
              <FileName>fsl_pxp.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_pxp.h</FilePath>
            </File>
            <File>
              <FileName>fsl_qtmr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_qtmr.c</FilePath>
            </File>
            <File>
              <FileName>fsl_qtmr.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_qtmr.h</FilePath>
            </File>
            <File>
              <FileName>fsl_rtwdog.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_rtwdog.c</FilePath>
            </File>
            <File>
              <FileName>fsl_rtwdog.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_rtwdog.h</FilePath>
            </File>
            <File>
              <FileName>fsl_sai.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_sai.c</FilePath>
            </File>
            <File>
              <FileName>fsl_sai.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_sai.h</FilePath>
            </File>
            <File>
              <FileName>fsl_sai_edma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_sai_edma.c</FilePath>
            </File>
            <File>
              <FileName>fsl_sai_edma.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_sai_edma.h</FilePath>
            </File>
            <File>
              <FileName>fsl_semc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_semc.c</FilePath>
            </File>
            <File>
              <FileName>fsl_semc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_semc.h</FilePath>
            </File>
            <File>
              <FileName>fsl_snvs_hp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_snvs_hp.c</FilePath>
            </File>
            <File>
              <FileName>fsl_snvs_hp.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_snvs_hp.h</FilePath>
            </File>
            <File>
              <FileName>fsl_snvs_lp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_snvs_lp.c</FilePath>
            </File>
            <File>
              <FileName>fsl_snvs_lp.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_snvs_lp.h</FilePath>
            </File>
            <File>
              <FileName>fsl_spdif.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_spdif.c</FilePath>
            </File>
            <File>
              <FileName>fsl_spdif.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_spdif.h</FilePath>
            </File>
            <File>
              <FileName>fsl_spdif_edma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_spdif_edma.c</FilePath>
            </File>
            <File>
              <FileName>fsl_spdif_edma.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_spdif_edma.h</FilePath>
            </File>
            <File>
              <FileName>fsl_src.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_src.c</FilePath>
            </File>
            <File>
              <FileName>fsl_src.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_src.h</FilePath>
            </File>
            <File>
              <FileName>fsl_tempmon.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_tempmon.c</FilePath>
            </File>
            <File>
              <FileName>fsl_tempmon.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_tempmon.h</FilePath>
            </File>
            <File>
              <FileName>fsl_trng.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_trng.c</FilePath>
            </File>
            <File>
              <FileName>fsl_trng.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_trng.h</FilePath>
            </File>
            <File>
              <FileName>fsl_tsc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_tsc.c</FilePath>
            </File>
            <File>
              <FileName>fsl_tsc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_tsc.h</FilePath>
            </File>
            <File>
              <FileName>fsl_usdhc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_usdhc.c</FilePath>
            </File>
            <File>
              <FileName>fsl_usdhc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_usdhc.h</FilePath>
            </File>
            <File>
              <FileName>fsl_wdog.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_wdog.c</FilePath>
            </File>
            <File>
              <FileName>fsl_wdog.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_wdog.h</FilePath>
            </File>
            <File>
              <FileName>fsl_xbara.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_xbara.c</FilePath>
            </File>
            <File>
              <FileName>fsl_xbara.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_xbara.h</FilePath>
            </File>
            <File>
              <FileName>fsl_xbarb.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_xbarb.c</FilePath>
            </File>
            <File>
              <FileName>fsl_xbarb.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\drives\fsl_xbarb.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>sdk_device</GroupName>
          <Files>
            <File>
              <FileName>board.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\board\board.c</FilePath>
            </File>
            <File>
              <FileName>board.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\board\board.h</FilePath>
            </File>
            <File>
              <FileName>clock_config.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\board\clock_config.c</FilePath>
            </File>
            <File>
              <FileName>clock_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\board\clock_config.h</FilePath>
            </File>
            <File>
              <FileName>fsl_flexspi_nor_boot.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\xip\fsl_flexspi_nor_boot.h</FilePath>
            </File>
            <File>
              <FileName>fsl_flexspi_nor_boot.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\xip\fsl_flexspi_nor_boot.c</FilePath>
            </File>
            <File>
              <FileName>evkmimxrt1064_sdram_ini_dcd.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\xip\evkmimxrt1064_sdram_ini_dcd.h</FilePath>
            </File>
            <File>
              <FileName>evkmimxrt1064_sdram_ini_dcd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\xip\evkmimxrt1064_sdram_ini_dcd.c</FilePath>
            </File>
            <File>
              <FileName>evkmimxrt1064_flexspi_nor_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\xip\evkmimxrt1064_flexspi_nor_config.h</FilePath>
            </File>
            <File>
              <FileName>evkmimxrt1064_flexspi_nor_config.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\xip\evkmimxrt1064_flexspi_nor_config.c</FilePath>
            </File>
            <File>
              <FileName>system_MIMXRT1064.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\deceive\system_MIMXRT1064.h</FilePath>
            </File>
            <File>
              <FileName>system_MIMXRT1064.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\deceive\system_MIMXRT1064.c</FilePath>
            </File>
            <File>
              <FileName>MIMXRT1064_features.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\deceive\MIMXRT1064_features.h</FilePath>
            </File>
            <File>
              <FileName>MIMXRT1064.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\deceive\MIMXRT1064.h</FilePath>
            </File>
            <File>
              <FileName>startup_MIMXRT1064.S</FileName>
              <FileType>2</FileType>
              <FilePath>..\..\libraries\sdk\startup\mdk\startup_MIMXRT1064.S</FilePath>
            </File>
            <File>
              <FileName>fsl_device_registers.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\deceive\fsl_device_registers.h</FilePath>
            </File>
            <File>
              <FileName>fsl_assert.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\utilities\fsl_assert.c</FilePath>
            </File>
            <File>
              <FileName>fsl_os_abstraction.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\components\osa\fsl_os_abstraction.h</FilePath>
            </File>
            <File>
              <FileName>fsl_os_abstraction_bm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\components\osa\fsl_os_abstraction_bm.c</FilePath>
            </File>
            <File>
              <FileName>fsl_os_abstraction_bm.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\components\osa\fsl_os_abstraction_bm.h</FilePath>
            </File>
            <File>
              <FileName>fsl_os_abstraction_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\components\osa\fsl_os_abstraction_config.h</FilePath>
            </File>
            <File>
              <FileName>fsl_component_generic_list.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\components\lists\fsl_component_generic_list.c</FilePath>
            </File>
            <File>
              <FileName>fsl_adapter_lpuart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\components\uart\fsl_adapter_lpuart.c</FilePath>
            </File>
            <File>
              <FileName>fsl_adapter_uart.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\components\uart\fsl_adapter_uart.h</FilePath>
            </File>
            <File>
              <FileName>fsl_debug_console.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\sdk\utilities\debug_console\fsl_debug_console.c</FilePath>
            </File>
            <File>
              <FileName>fsl_debug_console.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\utilities\debug_console\fsl_debug_console.h</FilePath>
            </File>
            <File>
              <FileName>fsl_debug_console_conf.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\sdk\utilities\debug_console\fsl_debug_console_conf.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>components_fatfs</GroupName>
          <Files>
            <File>
              <FileName>diskio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\components\fatfs\source\diskio.c</FilePath>
            </File>
            <File>
              <FileName>diskio.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\components\fatfs\source\diskio.h</FilePath>
            </File>
            <File>
              <FileName>ff.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\components\fatfs\source\ff.c</FilePath>
            </File>
            <File>
              <FileName>ff.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\components\fatfs\source\ff.h</FilePath>
            </File>
            <File>
              <FileName>ffconf.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\components\fatfs\source\ffconf.h</FilePath>
            </File>
            <File>
              <FileName>ffsystem.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\components\fatfs\source\ffsystem.c</FilePath>
            </File>
            <File>
              <FileName>ffunicode.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\components\fatfs\source\ffunicode.c</FilePath>
            </File>
            <File>
              <FileName>fsl_sd_disk.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\components\fatfs\source\fsl_sd_disk\fsl_sd_disk.c</FilePath>
            </File>
            <File>
              <FileName>fsl_sd_disk.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\components\fatfs\source\fsl_sd_disk\fsl_sd_disk.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>components_sdmmc</GroupName>
          <Files>
            <File>
              <FileName>sdmmc_config.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\components\sdmmc\sdmmc_config.c</FilePath>
            </File>
            <File>
              <FileName>sdmmc_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\components\sdmmc\sdmmc_config.h</FilePath>
            </File>
            <File>
              <FileName>fsl_sdmmc_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\components\sdmmc\common\fsl_sdmmc_common.c</FilePath>
            </File>
            <File>
              <FileName>fsl_sdmmc_common.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\components\sdmmc\common\fsl_sdmmc_common.h</FilePath>
            </File>
            <File>
              <FileName>fsl_sdmmc_spec.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\components\sdmmc\common\fsl_sdmmc_spec.h</FilePath>
            </File>
            <File>
              <FileName>fsl_sdmmc_host.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\components\sdmmc\host\usdhc\fsl_sdmmc_host.h</FilePath>
            </File>
            <File>
              <FileName>fsl_sdmmc_host.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\components\sdmmc\host\usdhc\non_blocking\fsl_sdmmc_host.c</FilePath>
            </File>
            <File>
              <FileName>fsl_sdmmc_osa.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\components\sdmmc\osa\fsl_sdmmc_osa.c</FilePath>
            </File>
            <File>
              <FileName>fsl_sdmmc_osa.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\components\sdmmc\osa\fsl_sdmmc_osa.h</FilePath>
            </File>
            <File>
              <FileName>fsl_sd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\components\sdmmc\sd\fsl_sd.c</FilePath>
            </File>
            <File>
              <FileName>fsl_sd.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\components\sdmmc\sd\fsl_sd.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>components_usb</GroupName>
          <Files>
            <File>
              <FileName>usb_device_ehci.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\components\usb\device\usb_device_ehci.c</FilePath>
            </File>
            <File>
              <FileName>usb_device_ehci.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\components\usb\device\usb_device_ehci.h</FilePath>
            </File>
            <File>
              <FileName>usb.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\components\usb\include\usb.h</FilePath>
            </File>
            <File>
              <FileName>usb_misc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\components\usb\include\usb_misc.h</FilePath>
            </File>
            <File>
              <FileName>usb_spec.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\components\usb\include\usb_spec.h</FilePath>
            </File>
            <File>
              <FileName>usb_phy.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\components\usb\phy\usb_phy.c</FilePath>
            </File>
            <File>
              <FileName>usb_phy.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\components\usb\phy\usb_phy.h</FilePath>
            </File>
            <File>
              <FileName>usb_device_dci.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\components\usb\device\usb_device_dci.c</FilePath>
            </File>
            <File>
              <FileName>usb_device_dci.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\components\usb\device\usb_device_dci.h</FilePath>
            </File>
            <File>
              <FileName>usb_device.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\components\usb\device\usb_device.h</FilePath>
            </File>
            <File>
              <FileName>usb_device_cdc_acm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\components\usb\usb_cdc_adapter\usb_device_cdc_acm.c</FilePath>
            </File>
            <File>
              <FileName>usb_device_cdc_acm.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\components\usb\usb_cdc_adapter\usb_device_cdc_acm.h</FilePath>
            </File>
            <File>
              <FileName>usb_device_ch9.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\components\usb\usb_cdc_adapter\usb_device_ch9.c</FilePath>
            </File>
            <File>
              <FileName>usb_device_ch9.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\components\usb\usb_cdc_adapter\usb_device_ch9.h</FilePath>
            </File>
            <File>
              <FileName>usb_device_class.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\components\usb\usb_cdc_adapter\usb_device_class.c</FilePath>
            </File>
            <File>
              <FileName>usb_device_class.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\components\usb\usb_cdc_adapter\usb_device_class.h</FilePath>
            </File>
            <File>
              <FileName>usb_device_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\components\usb\usb_cdc_adapter\usb_device_config.h</FilePath>
            </File>
            <File>
              <FileName>usb_device_descriptor.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\components\usb\usb_cdc_adapter\usb_device_descriptor.c</FilePath>
            </File>
            <File>
              <FileName>usb_device_descriptor.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\libraries\components\usb\usb_cdc_adapter\usb_device_descriptor.h</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components/>
    <files/>
  </RTE>

  <LayerInfo>
    <Layers>
      <Layer>
        <LayName>rt1064</LayName>
        <LayPrjMark>1</LayPrjMark>
      </Layer>
    </Layers>
  </LayerInfo>

</Project>
