/**
@page middleware_log Middleware Change Log
@section FatFs FatFs for MCUXpresso SDK
Current version is FatFs R0.13c_rev0.
  - R0.13c_rev0
      - Upgraded to version 0.13c
      - Apply patches ff_13c_p1.diff,ff_13c_p2.diff, ff_13c_p3.diff and ff_13c_p4.diff.
  - R0.13b_rev0
      - Upgraded to version 0.13b
  - R0.13a_rev0
      - Upgraded to version 0.13a. Added patch ff_13a_p1.diff.
  - R0.12c_rev1
      - Add NAND disk support.
  - R0.12c_rev0
      - Upgraded to version 0.12c and applied patches ff_12c_p1.diff and ff_12c_p2.diff.
  - R0.12b_rev0
      - Upgraded to version 0.12b.
  - R0.11a
      - Added glue functions for low-level drivers (SDHC, SDSPI, RAM, MMC). Modified diskio.c.
      - Added RTOS wrappers to make FatFs thread safe. Modified syscall.c.
      - Renamed ffconf.h to ffconf_template.h. Each application should contain its own ffconf.h.
      - Included ffconf.h into diskio.c to enable the selection of physical disk from ffconf.h by macro definition.
      - Conditional compilation of physical disk interfaces in diskio.c.
*/
